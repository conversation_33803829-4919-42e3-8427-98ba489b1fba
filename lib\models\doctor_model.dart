/// Doctor model class for storing and managing doctor information
/// Integrated with Supabase authentication and database
class Doctor<PERSON><PERSON><PERSON> {
  /// User ID from Supabase auth
  final String id;

  /// Required fields
  final String firstName;
  final String lastName;
  final String gender;
  final int phoneNumber;
  final String email;

  /// Optional fields with default values
  final String specialty;
  final String address;
  final int? licenseNumber;

  /// Timestamps
  final DateTime? createdAt;
  final DateTime? updatedAt;

  DoctorModel({
    // Required fields
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.phoneNumber,
    required this.email,

    // Optional fields with default values
    this.specialty = 'Unknown',
    this.address = 'Unknown',
    this.licenseNumber,

    // Timestamps
    this.createdAt,
    this.updatedAt,
  });

  /// Create DoctorModel from Supabase JSON
  factory DoctorModel.fromJson(Map<String, dynamic> json) {
    return DoctorModel(
      id: json['id'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      gender: json['gender'] as String,
      phoneNumber: json['phone_number'] as int,
      email: json['email'] as String,
      specialty: json['specialty'] as String? ?? 'Unknown',
      address: json['address'] as String? ?? 'Unknown',
      licenseNumber: json['license_number'] as int?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
    );
  }

  /// Convert DoctorModel to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'gender': gender,
      'phone_number': phoneNumber,
      'email': email,
      'specialty': specialty,
      'address': address,
      'license_number': licenseNumber,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  /// Create a copy of DoctorModel with updated fields
  DoctorModel copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? gender,
    int? phoneNumber,
    String? email,
    String? specialty,
    String? address,
    int? licenseNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DoctorModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      gender: gender ?? this.gender,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      specialty: specialty ?? this.specialty,
      address: address ?? this.address,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
