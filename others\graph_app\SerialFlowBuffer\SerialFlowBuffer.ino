#include <Arduino.h>
#include <SensirionI2CSdp.h>
#include <Wire.h>
#include <BluetoothSerial.h>
#include <ArduinoJson.h>

// Check if Bluetooth is available
#if !defined(CONFIG_BT_ENABLED) || !defined(CONFIG_BLUEDROID_ENABLED)
#error Bluetooth is not enabled! Please run `make menuconfig` to enable it
#endif

// Bluetooth Serial instance
BluetoothSerial SerialBT;

// I2C Configuration for ESP32-C6
#define I2C_SDA_PIN 6       // I2C SDA pin number
#define I2C_SCL_PIN 7       // I2C SCL pin number

// Sensor Configuration
SensirionI2CSdp sdp;        // Differential pressure sensor object

// Flow calculation parameters
const float K_FACTOR = 0.0001; // Sensor calibration factor
const float DENSITY = 1.204; // Air density at 20°C [kg/m³]

// Flow rate buffer for serial output
const int BUFFER_SIZE = 128; // Size of flow rate history buffer
float flowBuffer[BUFFER_SIZE] = {0}; // Circular buffer for flow rate data
int bufferIndex = 0;        // Current position in circular buffer

// Measurement control
bool isMeasuring = false;
unsigned long startTime = 0;
float totalVolume = 0.0;
unsigned long lastMeasurementTime = 0;
const int MEASUREMENT_INTERVAL_MS = 50; // 20Hz sampling rate

// Command processing
String inputString = "";
bool stringComplete = false;

void setup() {
  Serial.begin(115200);     // Initialize serial communication
  SerialBT.begin("ESP32_Flow_Sensor"); // Bluetooth device name
  Serial.println("ESP32 Flow Sensor started. Pair with your device.");

  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);

  // Configure pressure sensor
  sdp.begin(Wire, SDP3X_I2C_ADDRESS_0);
  sdp.stopContinuousMeasurement();
  delay(50);
  sdp.startContinuousMeasurementWithDiffPressureTCompAndAveraging();

  // Initialize input string
  inputString.reserve(64);
}

void loop() {
  // Check for incoming commands
  checkForCommands();

  // If we're measuring and it's time for a new sample
  if (isMeasuring && (millis() - lastMeasurementTime >= MEASUREMENT_INTERVAL_MS)) {
    lastMeasurementTime = millis();
    takeMeasurement();
  }
}

void checkForCommands() {
  // Read from Bluetooth Serial
  while (SerialBT.available()) {
    char inChar = (char)SerialBT.read();
    inputString += inChar;

    // Process command when newline received
    if (inChar == '\n') {
      stringComplete = true;
    }
  }

  // Process completed command
  if (stringComplete) {
    inputString.trim(); // Remove any whitespace

    if (inputString == "START_MEASUREMENT") {
      startMeasurement();
    } else if (inputString == "STOP_MEASUREMENT") {
      stopMeasurement();
    }

    // Clear the string for next command
    inputString = "";
    stringComplete = false;
  }
}

void startMeasurement() {
  Serial.println("Starting measurement");
  isMeasuring = true;
  startTime = millis();
  totalVolume = 0.0;

  // Clear buffer
  for (int i = 0; i < BUFFER_SIZE; i++) {
    flowBuffer[i] = 0.0;
  }
  bufferIndex = 0;
}

void stopMeasurement() {
  Serial.println("Stopping measurement");
  isMeasuring = false;
}

void takeMeasurement() {
  float differentialPressure, temperature;
  if(!sdp.readMeasurement(differentialPressure, temperature)) {
    // Calculate instantaneous flow rate in liters/second
    float flowRate = sqrt(fabs(differentialPressure) / DENSITY) * K_FACTOR * 1000;
    if(differentialPressure > 0) flowRate *= -1; // Adjust for sensor orientation

    // Calculate elapsed time in seconds
    float elapsedTime = (millis() - startTime) / 1000.0;

    // Calculate volume increment (flow * time interval)
    float volumeIncrement = flowRate * (MEASUREMENT_INTERVAL_MS / 1000.0);
    totalVolume += volumeIncrement;

    // Store in circular buffer
    flowBuffer[bufferIndex] = flowRate;
    bufferIndex = (bufferIndex + 1) % BUFFER_SIZE;

    // Create JSON document
    StaticJsonDocument<128> doc;
    doc["time"] = elapsedTime;
    doc["flow"] = flowRate;
    doc["volume"] = totalVolume;

    // Send JSON to Bluetooth
    String jsonString;
    serializeJson(doc, jsonString);
    SerialBT.println(jsonString);

    // Also send to Serial for debugging
    Serial.println(jsonString);
  }
}