/// Enum for treatment status
enum TreatmentStatus {
  active,
  completed,
  paused,
  cancelled;

  /// Get display name for the status
  String get displayName {
    switch (this) {
      case TreatmentStatus.active:
        return 'Active';
      case TreatmentStatus.completed:
        return 'Completed';
      case TreatmentStatus.paused:
        return 'Paused';
      case TreatmentStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get color for the status
  String get colorCode {
    switch (this) {
      case TreatmentStatus.active:
        return '#4CAF50'; // Green
      case TreatmentStatus.completed:
        return '#2196F3'; // Blue
      case TreatmentStatus.paused:
        return '#FF9800'; // Orange
      case TreatmentStatus.cancelled:
        return '#F44336'; // Red
    }
  }
}

/// Treatment model class for storing and managing treatment plans
/// Designed for future Supabase integration
class TreatmentModel {
  /// Unique identifier for the treatment
  final String? id;

  /// Required fields
  final String patientId;
  final String doctorId;
  final int numberOfUsesPerDay;
  final int numberOfDosesPerUse;
  final DateTime startDate;
  final DateTime endDate;

  /// Optional fields with default values
  final TreatmentStatus status;
  final String? sprayId; // Reference to SprayModel
  final String notes;
  final String treatmentName;

  /// Timestamps for tracking
  final DateTime? createdAt;
  final DateTime? updatedAt;

  TreatmentModel({
    this.id,
    // Required fields
    required this.patientId,
    required this.doctorId,
    required this.numberOfUsesPerDay,
    required this.numberOfDosesPerUse,
    required this.startDate,
    required this.endDate,
    // Optional fields with default values
    this.status = TreatmentStatus.active,
    this.sprayId,
    this.notes = '',
    this.treatmentName = 'Inhaler Treatment',
    this.createdAt,
    this.updatedAt,
  }) : assert(
         numberOfUsesPerDay > 0,
         'Number of uses per day must be positive',
       ),
       assert(
         numberOfDosesPerUse > 0,
         'Number of doses per use must be positive',
       ),
       assert(endDate.isAfter(startDate), 'End date must be after start date');

  /// Calculate treatment duration in days
  int get duration {
    return endDate.difference(startDate).inDays;
  }

  /// Calculate total doses for the entire treatment
  int get totalDoses {
    return numberOfUsesPerDay * numberOfDosesPerUse * duration;
  }

  /// Check if treatment is currently active based on dates
  bool get isCurrentlyActive {
    final now = DateTime.now();
    return status == TreatmentStatus.active &&
        now.isAfter(startDate) &&
        now.isBefore(endDate);
  }

  /// Get progress percentage (0-100)
  double get progressPercentage {
    final now = DateTime.now();
    if (now.isBefore(startDate)) return 0.0;
    if (now.isAfter(endDate)) return 100.0;

    final totalDuration = endDate.difference(startDate).inDays;
    final elapsedDuration = now.difference(startDate).inDays;

    return (elapsedDuration / totalDuration * 100).clamp(0.0, 100.0);
  }

  /// Get remaining days in treatment
  int get remainingDays {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }

  /// Create a copy of this treatment with some fields updated
  TreatmentModel copyWith({
    String? id,
    String? patientId,
    String? doctorId,
    int? numberOfUsesPerDay,
    int? numberOfDosesPerUse,
    DateTime? startDate,
    DateTime? endDate,
    TreatmentStatus? status,
    String? sprayId,
    String? notes,
    String? treatmentName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TreatmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      doctorId: doctorId ?? this.doctorId,
      numberOfUsesPerDay: numberOfUsesPerDay ?? this.numberOfUsesPerDay,
      numberOfDosesPerUse: numberOfDosesPerUse ?? this.numberOfDosesPerUse,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      sprayId: sprayId ?? this.sprayId,
      notes: notes ?? this.notes,
      treatmentName: treatmentName ?? this.treatmentName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to JSON for database storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'doctor_id': doctorId,
      'number_of_uses_per_day': numberOfUsesPerDay,
      'number_of_doses_per_use': numberOfDosesPerUse,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status.name,
      'spray_id': sprayId,
      'notes': notes,
      'treatment_name': treatmentName,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory TreatmentModel.fromJson(Map<String, dynamic> json) {
    return TreatmentModel(
      id: json['id'],
      patientId: json['patient_id'],
      doctorId: json['doctor_id'],
      numberOfUsesPerDay: json['number_of_uses_per_day'],
      numberOfDosesPerUse: json['number_of_doses_per_use'],
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      status: TreatmentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TreatmentStatus.active,
      ),
      sprayId: json['spray_id'],
      notes: json['notes'] ?? '',
      treatmentName: json['treatment_name'] ?? 'Inhaler Treatment',
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  /// String representation
  @override
  String toString() {
    return 'TreatmentModel(id: $id, patient: $patientId, '
        'uses: $numberOfUsesPerDay/day, doses: $numberOfDosesPerUse/use, '
        'duration: $duration days, status: ${status.displayName})';
  }

  /// Demo treatment data for testing and development
  static List<TreatmentModel> demoTreatments = [
    TreatmentModel(
      id: 'treatment_001',
      patientId: 'patient_001',
      doctorId: 'doctor_001',
      numberOfUsesPerDay: 4,
      numberOfDosesPerUse: 2,
      startDate: DateTime.now().subtract(const Duration(days: 7)),
      endDate: DateTime.now().add(const Duration(days: 23)),
      status: TreatmentStatus.active,
      treatmentName: 'Daily Bronchodilator Treatment',
      notes: 'Take 2 puffs 4 times daily. Monitor peak flow readings.',
      createdAt: DateTime.now().subtract(const Duration(days: 8)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    TreatmentModel(
      id: 'treatment_002',
      patientId: 'patient_002',
      doctorId: 'doctor_001',
      numberOfUsesPerDay: 2,
      numberOfDosesPerUse: 1,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now().subtract(const Duration(days: 1)),
      status: TreatmentStatus.completed,
      treatmentName: 'Controller Medication',
      notes: 'Maintenance therapy completed successfully.',
      createdAt: DateTime.now().subtract(const Duration(days: 31)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    TreatmentModel(
      id: 'treatment_003',
      patientId: 'patient_003',
      doctorId: 'doctor_002',
      numberOfUsesPerDay: 3,
      numberOfDosesPerUse: 2,
      startDate: DateTime.now().add(const Duration(days: 1)),
      endDate: DateTime.now().add(const Duration(days: 14)),
      status: TreatmentStatus.active,
      treatmentName: 'Short-term Relief Treatment',
      notes: 'Use as needed for symptom control.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    TreatmentModel(
      id: 'treatment_004',
      patientId: 'patient_001',
      doctorId: 'doctor_001',
      numberOfUsesPerDay: 2,
      numberOfDosesPerUse: 1,
      startDate: DateTime.now().subtract(const Duration(days: 5)),
      endDate: DateTime.now().add(const Duration(days: 10)),
      status: TreatmentStatus.paused,
      treatmentName: 'Combination Therapy',
      notes: 'Treatment paused due to side effects. Review in 3 days.',
      createdAt: DateTime.now().subtract(const Duration(days: 6)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
  ];
}
