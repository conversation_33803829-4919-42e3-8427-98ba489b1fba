CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL, -- 'patient' or 'doctor'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE patients (
    id UUID PRIMARY KEY REFERENCES users(id),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    cin BIGINT CHECK (cin >= 6000000 AND cin <= 13000000),
    tel TEXT,
    gender TEXT NOT NULL,
    birthday DATE NOT NULL,
    height DECIMAL(5,2) NOT NULL,
    weight DECIMAL(5,2) NOT NULL,
    sos_tel TEXT,
    notes TEXT,
    smoking_status TEXT,
    activity_level TEXT,
    blood_type TEXT,
    CONSTRAINT fk_user_id FOREIGN KEY (id) REFERENCES users(id)
);

CREATE TABLE doctors (
    id UUID PRIMARY KEY REFERENCES users(id),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    cin BIGINT CHECK (cin >= 6000000 AND cin <= 13000000),
    specialization TEXT,
    license_number INT,
    job_address TEXT,
    CONSTRAINT fk_user_id FOREIGN KEY (id) REFERENCES users(id)
);