import 'package:flutter/material.dart';
import 'dynamic_clock_widget.dart';

class MedicationReminderWidget extends StatelessWidget {
  const MedicationReminderWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Take the medicine in', 
            style: Theme.of(context).textTheme.bodyMedium
          ),
          Text(
            '30 minutes',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '2 left today',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).hintColor,
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: SizedBox(
              width: 80,
              height: 80,
              child: DynamicClockWidget(minutesRemaining: 30),
            ),
          ),
        ],
      ),
    );
  }
}
