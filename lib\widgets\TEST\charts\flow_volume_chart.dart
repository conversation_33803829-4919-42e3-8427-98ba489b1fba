import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

class FlowVolumeChart extends StatelessWidget {
  final List<MeasurementPoint> flowData;
  final List<MeasurementPoint> volumeData;
  final double? maxY;
  final double? minY;
  final double? maxX;
  final double? minX;
  final bool useDynamicScaling;

  const FlowVolumeChart({
    super.key,
    required this.flowData,
    required this.volumeData,
    this.maxY,
    this.minY,
    this.maxX,
    this.minX,
    this.useDynamicScaling = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(
        12.0,
      ), // Reduced padding to prevent overflow
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compact header for real-time display
          Row(
            children: [
              Icon(Icons.show_chart, color: AppTheme.primaryColor, size: 18),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  'Débit/Volume (Temps Réel)',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Flexible chart that takes remaining space
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SizedBox(
                  width: constraints.maxWidth,
                  height: constraints.maxHeight,
                  child: LineChart(_flowVolumeChartData()),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  LineChartData _flowVolumeChartData() {
    if (flowData.isEmpty || volumeData.isEmpty) {
      return LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(show: true),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300),
        ),
      );
    }

    List<FlSpot> spots = [];

    for (int i = 0; i < flowData.length && i < volumeData.length; i++) {
      if ((volumeData[i].value != 0 || spots.isEmpty) &&
          flowData[i].value < 0) {
        spots.add(FlSpot(volumeData[i].value, flowData[i].value));
      }
    }

    double effectiveMinY = 0.0;
    double effectiveMaxY = -5.0;
    double effectiveMinX = 0.0;
    double effectiveMaxX = 6.0;

    if (useDynamicScaling && spots.isNotEmpty) {
      List<double> yValues = spots.map((s) => s.y).toList();
      double dataMinY = yValues.reduce((a, b) => a < b ? a : b);
      double dataMaxY = yValues.reduce((a, b) => a > b ? a : b);

      double yRange = (dataMaxY - dataMinY).abs();
      double yPadding = yRange * 0.15;

      effectiveMaxY = 0.0;
      effectiveMinY = dataMinY - yPadding;

      List<double> xValues = spots.map((s) => s.x).toList();
      double dataMinX = xValues.reduce((a, b) => a < b ? a : b);
      double dataMaxX = xValues.reduce((a, b) => a > b ? a : b);

      double xRange = (dataMaxX - dataMinX).abs();
      double xPadding = xRange * 0.15;

      effectiveMinX = dataMinX > 0 ? 0 : dataMinX - xPadding;
      effectiveMaxX = dataMaxX + xPadding;
    }

    double yRange = (effectiveMaxY - effectiveMinY).abs();
    // Enhanced Y-axis interval calculation to prevent cluttered labels
    double yInterval;
    if (yRange > 50) {
      yInterval = 10.0;
    } else if (yRange > 25) {
      yInterval = 5.0;
    } else if (yRange > 15) {
      yInterval = 3.0;
    } else if (yRange > 10) {
      yInterval = 2.0;
    } else if (yRange > 6) {
      yInterval = 1.0;
    } else if (yRange > 3) {
      yInterval = 0.5;
    } else if (yRange > 1.5) {
      yInterval = 0.25;
    } else {
      yInterval = 0.1;
    }

    double xRange = (effectiveMaxX - effectiveMinX).abs();
    double xInterval =
        xRange > 10
            ? 2.0
            : xRange > 5
            ? 1.0
            : xRange > 2
            ? 0.5
            : 0.2;

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        drawHorizontalLine: true,
        horizontalInterval: yInterval,
        verticalInterval: xInterval,
        getDrawingHorizontalLine: (value) {
          return FlLine(color: Colors.grey.shade200, strokeWidth: 0.8);
        },
        getDrawingVerticalLine: (value) {
          return FlLine(color: Colors.grey.shade200, strokeWidth: 0.8);
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 25, // Reduced space for compact display
            interval: xInterval,
            getTitlesWidget: (value, meta) {
              String label =
                  xInterval < 1
                      ? value.toStringAsFixed(1)
                      : value.toInt().toString();
              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                      fontSize: 9, // Smaller font for compact display
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: yInterval,
            reservedSize: 50, // Reduced space to prevent overflow
            getTitlesWidget: (value, meta) {
              // Compact label formatting for real-time display
              String label;
              if (yInterval >= 1) {
                label = value.toInt().toString();
              } else if (yInterval >= 0.1) {
                label = value.toStringAsFixed(1);
              } else {
                label = value.toStringAsFixed(2);
              }

              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(right: 4.0),
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                      fontSize: 9, // Smaller font for compact display
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      minX: effectiveMinX,
      maxX: effectiveMaxX,
      minY: effectiveMinY,
      maxY: effectiveMaxY,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: AppTheme.primaryColor,
          barWidth: 2.5,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: AppTheme.primaryColor.withValues(alpha: 0.12),
            cutOffY: 0,
            applyCutOffY: true,
          ),
        ),
      ],
    );
  }
}
