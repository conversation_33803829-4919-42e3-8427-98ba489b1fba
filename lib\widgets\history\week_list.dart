import 'package:flutter/material.dart';
import 'package:befine/widgets/history/DayInfoPage.dart';

class WeekListView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Get the current date
    final DateTime now = DateTime.now();

    // Generate a list of the last 7 days (including today)
    final List<DateTime> lastSevenDays = List.generate(
      7,
      (index) => now.subtract(Duration(days: index)),
    );

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: lastSevenDays.length,
      itemBuilder: (context, index) {
        final day = lastSevenDays[index];
        return _buildDayCard(context, day, index);
      },
    );
  }

  Widget _buildDayCard(BuildContext context, DateTime day, int index) {
    // Format day name (e.g., "Monday")
    final String dayName = _getDayName(day.weekday);

    // Format date (e.g., "Jan 15, 2025")
    final String formattedDate =
        "${_getMonthName(day.month)} ${day.day}, ${day.year}";

    // Determine if this is today
    final bool isToday = index == 0;

    // Activity count (this would come from your data)
    final int activityCount =
        5 - index; // Just an example, replace with real data

    return Card(
      margin: EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => DayInfoPage(date: day)),
          );
        },
        borderRadius: BorderRadius.circular(15),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              // Date container with colored background
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isToday ? Colors.blue : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      day.day.toString(),
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: isToday ? Colors.white : Colors.black87,
                      ),
                    ),
                    Text(
                      dayName.substring(0, 3), // Abbreviated day name
                      style: TextStyle(
                        fontSize: 14,
                        color: isToday ? Colors.white : Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isToday
                          ? "Today, $formattedDate"
                          : "$dayName, $formattedDate",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      "$activityCount activities recorded",
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
              // Arrow icon
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to get day name
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return "Monday";
      case 2:
        return "Tuesday";
      case 3:
        return "Wednesday";
      case 4:
        return "Thursday";
      case 5:
        return "Friday";
      case 6:
        return "Saturday";
      case 7:
        return "Sunday";
      default:
        return "";
    }
  }

  // Helper method to get month name
  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return "Jan";
      case 2:
        return "Feb";
      case 3:
        return "Mar";
      case 4:
        return "Apr";
      case 5:
        return "May";
      case 6:
        return "Jun";
      case 7:
        return "Jul";
      case 8:
        return "Aug";
      case 9:
        return "Sep";
      case 10:
        return "Oct";
      case 11:
        return "Nov";
      case 12:
        return "Dec";
      default:
        return "";
    }
  }
}
