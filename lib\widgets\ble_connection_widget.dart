import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:provider/provider.dart';

/// Widget for displaying BLE connection status and controls
class BleConnectionWidget extends StatefulWidget {
  /// Constructor
  const BleConnectionWidget({super.key});

  @override
  State<BleConnectionWidget> createState() => _BleConnectionWidgetState();
}

class _BleConnectionWidgetState extends State<BleConnectionWidget> {
  bool _isScanning = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Check Bluetooth status on init
    _checkBluetoothStatus();
  }

  Future<void> _checkBluetoothStatus() async {
    try {
      if (await FlutterBluePlus.isSupported == false) {
        setState(() {
          _errorMessage = 'Bluetooth non pris en charge sur cet appareil';
        });
        return;
      }

      // Check if Bluetooth is on
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        setState(() {
          _errorMessage =
              'Veuillez activer le Bluetooth pour rechercher des appareils';
        });

        // Try to turn on Bluetooth on Android
        if (!Platform.isIOS && Platform.isAndroid) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            // User may have denied permission
            setState(() {
              _errorMessage =
                  'Veuillez activer le Bluetooth dans les paramètres';
            });
          }
        }
      } else {
        setState(() {
          _errorMessage = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation du Bluetooth: $e';
      });
    }
  }

  Future<void> _scanForDevices(BleManager bleManager) async {
    // Check Bluetooth status before scanning
    await _checkBluetoothStatus();
    if (!mounted) return;

    if (_errorMessage != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Réparer',
            textColor: Colors.white,
            onPressed: () async {
              if (Platform.isAndroid) {
                try {
                  await FlutterBluePlus.turnOn();
                  await _checkBluetoothStatus();
                } catch (e) {
                  // Ignore errors
                }
              }
            },
          ),
        ),
      );
      return;
    }

    setState(() {
      _isScanning = true;
      _errorMessage = null;
    });

    // Show scanning toast
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recherche d\'appareils ESP32...'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 1),
      ),
    );

    try {
      await bleManager.startScan(timeout: const Duration(seconds: 10));
      
      if (!mounted) return;

      setState(() {
        _isScanning = false;
      });

      if (bleManager.discoveredDevices.isEmpty) {
        setState(() {
          _errorMessage =
              'Aucun appareil ESP32 trouvé. Assurez-vous que votre appareil est allumé et à proximité.';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Aucun appareil ESP32 trouvé'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Réessayer',
              textColor: Colors.white,
              onPressed: () => _scanForDevices(bleManager),
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${bleManager.discoveredDevices.length} appareil(s) trouvé(s)'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isScanning = false;
        _errorMessage = 'Erreur de recherche: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur de recherche: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Réessayer',
            textColor: Colors.white,
            onPressed: () => _scanForDevices(bleManager),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bleManager = Provider.of<BleManager>(context);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Connexion BLE',
                  style: TextStyle(
                    fontSize: 18, 
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                _buildStatusIndicator(bleManager.connectionState),
              ],
            ),
            const SizedBox(height: 16),
            _buildConnectionContent(bleManager),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BleConnectionState state) {
    Color color;
    String text;

    switch (state) {
      case BleConnectionState.connected:
        color = Colors.green;
        text = 'Connecté';
        break;
      case BleConnectionState.connecting:
        color = Colors.orange;
        text = 'Connexion en cours';
        break;
      case BleConnectionState.scanning:
        color = Colors.blue;
        text = 'Recherche';
        break;
      case BleConnectionState.error:
        color = Colors.red;
        text = 'Erreur';
        break;
      case BleConnectionState.disconnected:
        color = Colors.grey;
        text = 'Déconnecté';
        break;
    }

    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(color: AppTheme.textPrimaryColor),
        ),
      ],
    );
  }

  Widget _buildConnectionContent(BleManager bleManager) {
    if (bleManager.connectionState == BleConnectionState.connected) {
      return _buildConnectedContent(bleManager);
    } else {
      return _buildScanContent(bleManager);
    }
  }

  Widget _buildConnectedContent(BleManager bleManager) {
    // Get device name or ID
    final deviceName =
        bleManager.connectedDevice?.platformName ?? 'Unknown Device';
    final deviceId = bleManager.connectedDevice?.remoteId.toString() ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Connecté à: $deviceName',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        if (deviceId.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              'ID: $deviceId',
              style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryColor),
            ),
          ),
        const SizedBox(height: 16),
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: () => bleManager.disconnectDevice(),
              icon: const Icon(Icons.bluetooth_disabled),
              label: const Text('Déconnecter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.warningColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScanContent(BleManager bleManager) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sélectionnez un appareil:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 0, 0, 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: const Color.fromRGBO(255, 0, 0, 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        const SizedBox(height: 8),
        SizedBox(
          height: 150,
          child:
              _isScanning
                  ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Recherche rapide du capteur de débit ESP32...'),
                      ],
                    ),
                  )
                  : bleManager.discoveredDevices.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bluetooth_searching,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage ??
                              'Aucun appareil trouvé. Appuyez sur "Rechercher" pour lancer la recherche.',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    shrinkWrap: true,
                    itemCount: bleManager.discoveredDevices.length,
                    itemBuilder: (context, index) {
                      final device = bleManager.discoveredDevices[index];
                      final deviceName =
                          device.platformName.isNotEmpty
                              ? device.platformName
                              : device.advName.isNotEmpty
                              ? device.advName
                              : 'Unknown Device';

                      return ListTile(
                        leading: Icon(
                          Icons.bluetooth,
                          color: AppTheme.primaryColor,
                        ),
                        title: Text(
                          deviceName,
                          style: TextStyle(color: AppTheme.textPrimaryColor),
                        ),
                        subtitle: Text(
                          device.remoteId.toString(),
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () => _connectToDevice(device, bleManager),
                      );
                    },
                  ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton.icon(
              onPressed: _isScanning ? null : () => _scanForDevices(bleManager),
              icon: const Icon(Icons.search),
              label: Text(
                _isScanning ? 'Recherche rapide...' : 'Rechercher rapidement',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey,
              ),
            ),
            if (Platform.isAndroid)
              TextButton.icon(
                onPressed: () async {
                  try {
                    await FlutterBluePlus.turnOn();
                    await _checkBluetoothStatus();
                    if (!mounted) return;
                  } catch (e) {
                    if (!mounted) return;
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('Error: $e')));
                  }
                },
                icon: Icon(Icons.bluetooth, color: AppTheme.primaryColor),
                label: Text(
                  'ON',
                  style: TextStyle(color: AppTheme.primaryColor),
                ),
              ),
          ],
        ),
      ],
    );
  }

  void _connectToDevice(BluetoothDevice device, BleManager bleManager) async {
    setState(() {
      _errorMessage = null;
      _isScanning = false; // Stop scanning UI indicator
    });

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show connecting dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Connexion'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                "Connexion à ${device.platformName.isNotEmpty ? device.platformName : "l'appareil"}...",
              ),
              const SizedBox(height: 8),
              const Text(
                'Cela peut prendre quelques instants.',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      },
    );

    try {
      final success = await bleManager.connectToDevice(device);

      // Close the dialog
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!mounted) return;

      if (!success) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text(
              'Échec de la connexion à l\'appareil. Veuillez réessayer.',
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Connecté avec succès!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Close the dialog
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Erreur de connexion: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Réessayer',
            textColor: Colors.white,
            onPressed: () => _connectToDevice(device, bleManager),
          ),
        ),
      );
    }
  }
}
