import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'dart:math';

class HistoryDayDetail extends StatelessWidget {
  final DateTime date;
  final Random random = Random();

  // This would normally come from your database based on the date
  late final int numberOfUsages;
  late final int totalDoses = 5; // Total treatment doses in a day
  final List<String> timeLabels = ["Morning", "Noon", "Evening", "Night"];
  final List<Map<String, dynamic>> devices = [
    {
      "name": "BeFine_01",
      "dose": "100/50 mcg",
      "gradient": [const Color.fromARGB(255, 94, 8, 255), Colors.blueAccent],
    },
    {
      "name": "BeFine_02",
      "dose": "250/50 mcg",
      "gradient": [
        const Color.fromARGB(255, 255, 47, 210),
        const Color.fromARGB(255, 255, 60, 0),
      ],
    },
  ];

  HistoryDayDetail({Key? key, required this.date}) : super(key: key) {
    // Simulate different usage data for different days
    // In a real app, you would fetch this from your database
    final daysSinceEpoch = date.difference(DateTime(1970, 1, 1)).inDays;
    numberOfUsages = 1 + (daysSinceEpoch % 5); // Between 1 and 5 usages
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          formatDate(date),
          style: TextStyle(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.surfaceColor,
        iconTheme: IconThemeData(color: AppTheme.textPrimaryColor),
        elevation: 0,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildContinuousProgressBar(),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: numberOfUsages,
              itemBuilder: (context, index) {
                return _buildUsageCard(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  String formatDate(DateTime date) {
    final months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    final days = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];

    bool isToday =
        DateTime.now().year == date.year &&
        DateTime.now().month == date.month &&
        DateTime.now().day == date.day;

    if (isToday) {
      return "Today, ${months[date.month - 1]} ${date.day}, ${date.year}";
    }

    return "${days[date.weekday - 1]}, ${months[date.month - 1]} ${date.day}, ${date.year}";
  }

  Widget _buildContinuousProgressBar() {
    double progress = numberOfUsages / totalDoses;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Treatment Progress",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children:
              timeLabels
                  .map(
                    (label) => Text(
                      label,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  )
                  .toList(),
        ),
        const SizedBox(height: 6),
        Stack(
          alignment: Alignment.centerLeft,
          children: [
            Container(
              height: 12,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            LayoutBuilder(
              builder:
                  (context, constraints) => Container(
                    width: constraints.maxWidth * progress,
                    height: 12,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.secondaryColor,
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(totalDoses, (index) {
                bool isCompleted = index < numberOfUsages;
                return Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        isCompleted
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                    border: Border.all(
                      color: isCompleted ? Colors.white : Colors.redAccent,
                      width: 2,
                    ),
                  ),
                  child:
                      isCompleted
                          ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 8,
                          )
                          : null,
                );
              }),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUsageCard(int index) {
    final device = devices[random.nextInt(devices.length)];

    // Generate different times based on index
    final hour = 8 + (index * 3); // 8 AM, 11 AM, 2 PM, etc.
    final minute = random.nextInt(60);
    final amPm = hour >= 12 ? "PM" : "AM";
    final hourDisplay = hour > 12 ? hour - 12 : hour;
    final timeDisplay =
        "$hourDisplay:${minute.toString().padLeft(2, '0')} $amPm";

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: device["gradient"],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      device["name"],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      device["dose"],
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      timeDisplay,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        const Icon(
                          Icons.battery_full,
                          color: Colors.green,
                          size: 20,
                        ),
                        const SizedBox(width: 5),
                        Text(
                          "${85 - (index * 5)}%", // Different battery levels
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 10),
            Column(
              children: [
                _buildReadingCard(
                  "CO₂",
                  Icons.cloud,
                  450 + random.nextInt(40) - 20,
                  440 + random.nextInt(40) - 20,
                ),
                _buildReadingCard(
                  "PEF",
                  Icons.speed,
                  300 + random.nextInt(30) - 15,
                  320 + random.nextInt(30) - 15,
                ),
                _buildReadingCard(
                  "FEV",
                  Icons.air,
                  2.5 + random.nextDouble() * 0.4 - 0.2,
                  2.3 + random.nextDouble() * 0.4 - 0.2,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingCard(
    String name,
    IconData icon,
    double current,
    double previous,
  ) {
    bool isIncreased = current >= previous;

    // Format the numbers differently for FEV (with decimal places)
    String currentDisplay =
        name == "FEV" ? current.toStringAsFixed(1) : current.toStringAsFixed(0);
    String previousDisplay =
        name == "FEV"
            ? previous.toStringAsFixed(1)
            : previous.toStringAsFixed(0);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 28),
                const SizedBox(width: 10),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      currentDisplay,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      "Prev: $previousDisplay",
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 8),
                Icon(
                  isIncreased ? Icons.arrow_upward : Icons.arrow_downward,
                  color:
                      isIncreased ? AppTheme.successColor : AppTheme.errorColor,
                  size: 24,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
