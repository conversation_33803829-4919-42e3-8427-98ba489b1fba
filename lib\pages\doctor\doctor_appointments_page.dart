import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';

class DoctorAppointmentsPage extends StatefulWidget {
  const DoctorAppointmentsPage({Key? key}) : super(key: key);

  @override
  State<DoctorAppointmentsPage> createState() => _DoctorAppointmentsPageState();
}

class _DoctorAppointmentsPageState extends State<DoctorAppointmentsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appointments'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Today'),
            Tab(text: 'Past'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // Implement calendar view
            },
          ),
        ],
      ),
      body: Tab<PERSON><PERSON><PERSON>ie<PERSON>(
        controller: _tabController,
        children: [
          _buildAppointmentsList('upcoming'),
          _buildAppointmentsList('today'),
          _buildAppointmentsList('past'),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'doctor_appointments_fab',
        onPressed: () {
          // Implement new appointment creation
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAppointmentsList(String type) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: 5, // Replace with actual appointment count
      itemBuilder: (context, index) {
        return _buildAppointmentCard();
      },
    );
  }

  Widget _buildAppointmentCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          child: Text('JD'),
          backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
        ),
        title: const Text('John Doe'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: const [
                Icon(Icons.access_time, size: 16),
                SizedBox(width: 8),
                Text('09:30 AM - 10:00 AM'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: const [
                Icon(Icons.medical_services, size: 16),
                SizedBox(width: 8),
                Text('Regular Checkup'),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'reschedule',
                  child: Text('Reschedule'),
                ),
                const PopupMenuItem(value: 'cancel', child: Text('Cancel')),
              ],
          onSelected: (value) {
            // Handle menu item selection
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
