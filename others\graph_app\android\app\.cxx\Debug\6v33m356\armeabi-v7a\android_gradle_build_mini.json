{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\PFE\\VSC\\TEST\\Docteur\\SerialFlowBuffer\\graph_app\\android\\app\\.cxx\\Debug\\6v33m356\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\PFE\\VSC\\TEST\\Docteur\\SerialFlowBuffer\\graph_app\\android\\app\\.cxx\\Debug\\6v33m356\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}