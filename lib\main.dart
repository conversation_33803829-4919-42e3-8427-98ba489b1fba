import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:befine/routes/app_routes.dart';
import 'package:befine/config/supabase_config.dart';
import 'package:befine/pages/auth/auth_wrapper.dart';

import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/services/weather_service.dart';
import 'package:befine/services/auth_service.dart';
import 'package:befine/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await SupabaseConfig.initialize();

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthService()),
        ChangeNotifierProvider(create: (context) => BleManager()),
        ChangeNotifierProvider(create: (context) => DeviceService()),
        ChangeNotifierProvider(create: (context) => WeatherService()),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          useMaterial3: true,
          scaffoldBackgroundColor: AppTheme.backgroundColor,
          appBarTheme: AppBarTheme(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
        home: const AuthWrapper(),
        onGenerateRoute: AppRoutes.generateRoute,
      ),
    );
  }
}
