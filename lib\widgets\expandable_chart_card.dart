import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';

/// A card that can be expanded to show a chart
class ExpandableChartCard extends StatefulWidget {
  /// Title of the card
  final String title;
  
  /// Chart widget to display when expanded
  final Widget chart;
  
  /// Whether the card is initially expanded
  final bool initiallyExpanded;

  /// Constructor
  const ExpandableChartCard({
    super.key,
    required this.title,
    required this.chart,
    this.initiallyExpanded = false,
  });

  @override
  State<ExpandableChartCard> createState() => _ExpandableChartCardState();
}

class _ExpandableChartCardState extends State<ExpandableChartCard> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12.0)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppTheme.primaryColor,
                  ),
                ],
              ),
            ),
          ),
          
          // Chart content
          AnimatedCrossFade(
            firstChild: const SizedBox(height: 0),
            secondChild: widget.chart,
            crossFadeState: _isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }
}
