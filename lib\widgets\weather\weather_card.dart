import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:befine/services/weather_service.dart';
import 'package:befine/models/weather_data.dart';
import 'package:befine/theme/app_theme.dart';

/// Weather card widget that displays current weather information
class WeatherCard extends StatefulWidget {
  const WeatherCard({super.key});

  @override
  State<WeatherCard> createState() => _WeatherCardState();
}

class _WeatherCardState extends State<WeatherCard> {
  @override
  void initState() {
    super.initState();
    // Fetch weather data when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchWeatherData();
    });
  }

  /// Fetch weather data
  void _fetchWeatherData() {
    final weatherService = Provider.of<WeatherService>(context, listen: false);
    weatherService.getCurrentWeather();
  }

  /// Refresh weather data
  Future<void> _refreshWeather() async {
    final weatherService = Provider.of<WeatherService>(context, listen: false);
    await weatherService.refreshWeather();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WeatherService>(
      builder: (context, weatherService, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: _getBackgroundGradient(weatherService.currentWeather),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _refreshWeather,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: _buildContent(weatherService),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build card content based on weather service state
  Widget _buildContent(WeatherService weatherService) {
    if (weatherService.isLoading) {
      return _buildLoadingState();
    } else if (weatherService.error != null) {
      return _buildErrorState(weatherService.error!);
    } else if (weatherService.currentWeather != null) {
      return _buildWeatherContent(weatherService.currentWeather!);
    } else {
      return _buildEmptyState();
    }
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
        SizedBox(height: 16),
        Text(
          'Chargement de la météo...',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Build error state
  Widget _buildErrorState(String error) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.error_outline, color: Colors.white, size: 48),
        const SizedBox(height: 16),
        Text(
          'Erreur météo',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Appuyez pour réessayer',
          style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 14),
        ),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.wb_sunny_outlined, color: Colors.white, size: 48),
        const SizedBox(height: 16),
        const Text(
          'Météo',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Appuyez pour charger',
          style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 14),
        ),
      ],
    );
  }

  /// Build weather content
  Widget _buildWeatherContent(WeatherData weather) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header with location and refresh
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    weather.cityName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (weather.country.isNotEmpty)
                    Text(
                      weather.country,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                  // Offline/cached data indicator
                  Consumer<WeatherService>(
                    builder: (context, service, child) {
                      if (service.isOffline || service.isUsingCachedData) {
                        return Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                service.isOffline
                                    ? Colors.orange.withOpacity(0.8)
                                    : Colors.blue.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                service.isOffline
                                    ? Icons.wifi_off
                                    : Icons.access_time,
                                color: Colors.white,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                service.isOffline
                                    ? 'Hors ligne'
                                    : weather.formattedLastUpdated,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: _refreshWeather,
              icon: const Icon(Icons.refresh, color: Colors.white),
              tooltip: 'Actualiser',
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Main weather info
        Row(
          children: [
            // Temperature and icon
            Expanded(
              child: Row(
                children: [
                  Text(
                    weather.weatherIcon,
                    style: const TextStyle(fontSize: 48),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${weather.temperatureCelsius}°C',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        weather.formattedDescription,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Additional weather details
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildWeatherDetail(
              icon: Icons.thermostat,
              label: 'Ressenti',
              value: '${weather.feelsLikeCelsius}°C',
            ),
            _buildWeatherDetail(
              icon: Icons.water_drop,
              label: 'Humidité',
              value: weather.formattedHumidity,
            ),
            _buildWeatherDetail(
              icon: Icons.air,
              label: 'Vent',
              value: weather.formattedWindSpeed,
            ),
          ],
        ),
      ],
    );
  }

  /// Build weather detail item
  Widget _buildWeatherDetail({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, color: Colors.white.withOpacity(0.8), size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Get background gradient based on weather
  LinearGradient _getBackgroundGradient(WeatherData? weather) {
    if (weather != null) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: weather.backgroundColors,
      );
    }

    // Default gradient
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF87CEEB), // Sky blue
        Color(0xFF4682B4), // Steel blue
      ],
    );
  }
}
