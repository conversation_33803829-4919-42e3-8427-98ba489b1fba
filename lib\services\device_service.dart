import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/models/device_model.dart';

/// Service for managing device persistence and state
class DeviceService extends ChangeNotifier {
  static const String _devicesKey = 'saved_devices';
  static const String _lastConnectedDeviceKey = 'last_connected_device';
  static const String _autoConnectEnabledKey = 'auto_connect_enabled';

  List<Device> _devices = [];
  List<Device> get devices => List.unmodifiable(_devices);

  String? _lastConnectedDeviceId;
  String? get lastConnectedDeviceId => _lastConnectedDeviceId;

  bool _autoConnectEnabled = true;
  bool get autoConnectEnabled => _autoConnectEnabled;

  /// Initialize the service and load saved devices
  Future<void> initialize() async {
    await _loadDevices();
    await _loadLastConnectedDevice();
    await _loadAutoConnectSettings();
  }

  /// Load devices from SharedPreferences
  Future<void> _loadDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? devicesJson = prefs.getString(_devicesKey);

      if (devicesJson != null) {
        final List<dynamic> devicesList = json.decode(devicesJson);
        _devices =
            devicesList.map((deviceMap) => Device.fromJson(deviceMap)).toList();
        debugPrint('Loaded ${_devices.length} devices from storage');
      } else {
        // Initialize with default devices if no saved devices exist
        _initializeDefaultDevices();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading devices: $e');
      _initializeDefaultDevices();
    }
  }

  /// Save devices to SharedPreferences
  Future<void> _saveDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> devicesList =
          _devices.map((device) => device.toJson()).toList();
      final String devicesJson = json.encode(devicesList);

      await prefs.setString(_devicesKey, devicesJson);
      debugPrint('Saved ${_devices.length} devices to storage');
    } catch (e) {
      debugPrint('Error saving devices: $e');
    }
  }

  /// Load last connected device from SharedPreferences
  Future<void> _loadLastConnectedDevice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lastConnectedDeviceId = prefs.getString(_lastConnectedDeviceKey);
      debugPrint('Loaded last connected device: $_lastConnectedDeviceId');
    } catch (e) {
      debugPrint('Error loading last connected device: $e');
    }
  }

  /// Load auto-connect settings from SharedPreferences
  Future<void> _loadAutoConnectSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _autoConnectEnabled = prefs.getBool(_autoConnectEnabledKey) ?? true;
      debugPrint('Loaded auto-connect setting: $_autoConnectEnabled');
    } catch (e) {
      debugPrint('Error loading auto-connect settings: $e');
    }
  }

  /// Save last connected device to SharedPreferences
  Future<void> _saveLastConnectedDevice(String? deviceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (deviceId != null) {
        await prefs.setString(_lastConnectedDeviceKey, deviceId);
        debugPrint('Saved last connected device: $deviceId');
      } else {
        await prefs.remove(_lastConnectedDeviceKey);
        debugPrint('Cleared last connected device');
      }
      _lastConnectedDeviceId = deviceId;
    } catch (e) {
      debugPrint('Error saving last connected device: $e');
    }
  }

  /// Save auto-connect settings to SharedPreferences
  Future<void> _saveAutoConnectSettings(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoConnectEnabledKey, enabled);
      _autoConnectEnabled = enabled;
      debugPrint('Saved auto-connect setting: $enabled');
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving auto-connect settings: $e');
    }
  }

  /// Initialize with default sample devices
  void _initializeDefaultDevices() {
    _devices = [
      Device(
        deviceType: 'BeFine_Spray',
        batteryLevel: 85,
        storageStatus: StorageStatus.ok,
        lastSync: null,
        deviceId: '1548',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        isConnected: false,
        connectionType: ConnectionType.none,
        customName: 'My Red Device',
        bleMacAddress: '11:22:33:44:55:66',
      ),
      Device(
        deviceType: 'BeFine_Spray',
        batteryLevel: 65,
        storageStatus: StorageStatus.full,
        lastSync: null,
        deviceId: '1549',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
        isConnected: false,
        connectionType: ConnectionType.none,
        customName: 'My Blue Device',
        bleMacAddress: 'AA:BB:CC:DD:EE:FF',
      ),
    ];

    // Save the default devices
    _saveDevices();
  }

  /// Add a new device or update existing one
  Future<void> addOrUpdateDevice(Device device) async {
    final existingIndex = _devices.indexWhere(
      (d) =>
          d.deviceId == device.deviceId ||
          d.bleMacAddress == device.bleMacAddress,
    );

    if (existingIndex >= 0) {
      // Update existing device
      _devices[existingIndex] = device;
      debugPrint('Updated existing device: ${device.deviceId}');
    } else {
      // Add new device
      _devices.add(device);
      debugPrint('Added new device: ${device.deviceId}');
    }

    await _saveDevices();
    notifyListeners();
  }

  /// Remove a device
  Future<void> removeDevice(String deviceId) async {
    _devices.removeWhere((device) => device.deviceId == deviceId);
    await _saveDevices();
    notifyListeners();
  }

  /// Update device connection status
  Future<void> updateDeviceConnectionStatus(
    String deviceId,
    bool isConnected,
  ) async {
    final deviceIndex = _devices.indexWhere((d) => d.deviceId == deviceId);

    if (deviceIndex >= 0) {
      final device = _devices[deviceIndex];
      _devices[deviceIndex] = device.copyWith(
        isConnected: isConnected,
        connectionType: isConnected ? ConnectionType.ble : ConnectionType.none,
        lastSync: isConnected ? DateTime.now() : device.lastSync,
        updatedAt: DateTime.now(),
      );

      // Update last connected device when connecting
      if (isConnected) {
        await _saveLastConnectedDevice(deviceId);
      }

      await _saveDevices();
      notifyListeners();
      debugPrint(
        'Updated connection status for device $deviceId: $isConnected',
      );
    }
  }

  /// Update device battery level
  Future<void> updateDeviceBatteryLevel(
    String deviceId,
    int batteryLevel,
  ) async {
    final deviceIndex = _devices.indexWhere((d) => d.deviceId == deviceId);

    if (deviceIndex >= 0) {
      final device = _devices[deviceIndex];
      _devices[deviceIndex] = device.copyWith(
        batteryLevel: batteryLevel,
        updatedAt: DateTime.now(),
      );

      await _saveDevices();
      notifyListeners();
      debugPrint('Updated battery level for device $deviceId: $batteryLevel%');
    }
  }

  /// Get device by ID
  Device? getDeviceById(String deviceId) {
    try {
      return _devices.firstWhere((device) => device.deviceId == deviceId);
    } catch (e) {
      return null;
    }
  }

  /// Get connected devices
  List<Device> getConnectedDevices() {
    return _devices.where((device) => device.isConnected == true).toList();
  }

  /// Get devices by connection type
  List<Device> getDevicesByConnectionType(ConnectionType connectionType) {
    return _devices
        .where((device) => device.connectionType == connectionType)
        .toList();
  }

  /// Update device custom name
  Future<void> updateDeviceName(String deviceId, String customName) async {
    final deviceIndex = _devices.indexWhere((d) => d.deviceId == deviceId);

    if (deviceIndex >= 0) {
      final device = _devices[deviceIndex];
      _devices[deviceIndex] = device.copyWith(
        customName: customName,
        updatedAt: DateTime.now(),
      );

      await _saveDevices();
      notifyListeners();
      debugPrint('Updated name for device $deviceId: $customName');
    }
  }

  /// Clear all devices (for testing or reset)
  Future<void> clearAllDevices() async {
    _devices.clear();
    await _saveDevices();
    notifyListeners();
    debugPrint('Cleared all devices');
  }

  /// Sync device data (placeholder for future implementation)
  Future<void> syncDevice(String deviceId) async {
    final deviceIndex = _devices.indexWhere((d) => d.deviceId == deviceId);

    if (deviceIndex >= 0) {
      final device = _devices[deviceIndex];
      _devices[deviceIndex] = device.copyWith(
        lastSync: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _saveDevices();
      notifyListeners();
      debugPrint('Synced device: $deviceId');
    }
  }

  /// Get the last connected device
  Device? getLastConnectedDevice() {
    if (_lastConnectedDeviceId != null) {
      return getDeviceById(_lastConnectedDeviceId!);
    }
    return null;
  }

  /// Set auto-connect enabled/disabled
  Future<void> setAutoConnectEnabled(bool enabled) async {
    await _saveAutoConnectSettings(enabled);
  }

  /// Clear last connected device (when manually disconnecting)
  Future<void> clearLastConnectedDevice() async {
    await _saveLastConnectedDevice(null);
  }
}
