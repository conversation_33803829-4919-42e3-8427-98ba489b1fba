import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/models/test_results.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';

class PdfService {
  // Generate PDF from test data
  Future<Uint8List> generatePdf({
    required MeasurementData measurementData,
    required TestResults testResults,
    required BuildContext context,
  }) async {
    // Create a PDF document
    final pdf = pw.Document();

    // Get current date and time
    final now = DateTime.now();
    final dateStr = '${now.day}/${now.month}/${now.year}';
    final timeStr = '${now.hour}:${now.minute}';

    // Add content to PDF
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (pw.Context context) {
          return pw.Header(
            level: 0,
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  'Rapport de Test Respiratoire',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text('Date: $dateStr'),
                    pw.Text('Heure: $timeStr'),
                  ],
                ),
              ],
            ),
          );
        },
        footer: (pw.Context context) {
          return pw.Footer(
            trailing: pw.Text(
              'Page ${context.pageNumber} sur ${context.pagesCount}',
              style: const pw.TextStyle(fontSize: 12, color: PdfColors.grey),
            ),
          );
        },
        build:
            (pw.Context context) => [
              // Test Results Section
              pw.Header(level: 1, text: 'Résultats du Test'),
              pw.SizedBox(height: 10),
              _buildResultsTable(testResults),
              pw.SizedBox(height: 20),

              // Flow/Volume Chart Section
              pw.Header(level: 1, text: 'Courbe Débit/Volume'),
              pw.SizedBox(height: 10),
              pw.Paragraph(
                text:
                    'La courbe Débit/Volume montre la relation entre le débit inspiratoire et le volume pulmonaire pendant l\'inhalation. Le DIP (Débit Inspiratoire de Pointe) est de ${(-testResults.dip).toStringAsFixed(2)} L/s à ${testResults.dipTime.toStringAsFixed(2)} secondes.',
              ),
              pw.SizedBox(height: 10),
              _buildFlowVolumeDescription(testResults),
              pw.SizedBox(height: 10),
              _buildFlowVolumeChart(measurementData),
            ],
      ),
    );

    // Return the PDF document as bytes
    return pdf.save();
  }

  // Build a table with test results
  pw.Widget _buildResultsTable(TestResults results) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // Table header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('Paramètre', isHeader: true),
            _buildTableCell('Valeur', isHeader: true),
          ],
        ),
        // DIP
        pw.TableRow(
          children: [
            _buildTableCell('DIP (Débit Inspiratoire de Pointe)'),
            _buildTableCell('${(-results.dip).toStringAsFixed(2)} L/s'),
          ],
        ),
        // Inhalation Duration
        pw.TableRow(
          children: [
            _buildTableCell('Durée d\'Inspiration'),
            _buildTableCell(
              '${results.inhalationDuration.toStringAsFixed(2)} s',
            ),
          ],
        ),
        // DIP Time
        pw.TableRow(
          children: [
            _buildTableCell('Temps du DIP'),
            _buildTableCell('${results.dipTime.toStringAsFixed(2)} s'),
          ],
        ),
        // Maximum Volume
        pw.TableRow(
          children: [
            _buildTableCell('Volume Maximum'),
            _buildTableCell('${results.maxVolume.toStringAsFixed(2)} L'),
          ],
        ),
        // Inhalation Start/End
        pw.TableRow(
          children: [
            _buildTableCell('Début/Fin d\'Inspiration'),
            _buildTableCell(
              '${results.inhalationStartTime.toStringAsFixed(2)} s - ${results.inhalationEndTime.toStringAsFixed(2)} s',
            ),
          ],
        ),
      ],
    );
  }

  // Build a description of the Flow/Volume curve
  pw.Widget _buildFlowVolumeDescription(TestResults results) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Caractéristiques de la Courbe Débit/Volume:',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            '• DIP (Débit Inspiratoire de Pointe): ${(-results.dip).toStringAsFixed(2)} L/s',
          ),
          pw.Text(
            '• Temps pour atteindre le DIP: ${results.dipTime.toStringAsFixed(2)} s',
          ),
          pw.Text(
            '• Volume Maximum: ${results.maxVolume.toStringAsFixed(2)} L',
          ),
          pw.Text(
            '• Durée d\'Inspiration: ${results.inhalationDuration.toStringAsFixed(2)} s',
          ),
        ],
      ),
    );
  }

  // Build a flow/volume chart directly in the PDF
  pw.Widget _buildFlowVolumeChart(MeasurementData data) {
    // If we don't have enough data, return empty chart with message
    if (data.flowData.isEmpty || data.volumeData.isEmpty) {
      return pw.Container(
        height: 300,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        ),
        child: pw.Center(
          child: pw.Text(
            'Pas de données disponibles',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey,
            ),
          ),
        ),
      );
    }

    // Create paired data points (volume, flow)
    // We need to match flow and volume data points that have the same time
    List<pw.PointChartValue> spots = [];

    // For inhalation, flow values are negative, and we'll keep them negative
    for (
      int i = 0;
      i < data.flowData.length && i < data.volumeData.length;
      i++
    ) {
      // Only add points if we have valid volume (x-axis) data and if it's inhalation (negative flow)
      if ((data.volumeData[i].value != 0 || spots.isEmpty) &&
          data.flowData[i].value < 0) {
        // Keep the flow value negative for proper display with zero at top
        spots.add(
          pw.PointChartValue(data.volumeData[i].value, data.flowData[i].value),
        );
      }
    }

    // If no valid spots were created, return empty chart with message
    if (spots.isEmpty) {
      return pw.Container(
        height: 300,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        ),
        child: pw.Center(
          child: pw.Text(
            'Pas de données d\'inhalation disponibles',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey,
            ),
          ),
        ),
      );
    }

    // Calculate min and max values for axes
    double minX = 0;
    double maxX = 0;
    double minY = 0;
    double maxY = 0;

    // Find min and max values in the data
    for (var spot in spots) {
      if (spot.x < minX) minX = spot.x;
      if (spot.x > maxX) maxX = spot.x;
      if (spot.y < minY) minY = spot.y;
      if (spot.y > maxY) maxY = spot.y;
    }

    // Add some padding
    maxX = maxX + (maxX * 0.1);
    minY = minY + (minY * 0.1); // More negative

    // For volume, we typically want to start at 0 unless we have negative values
    if (minX > 0) minX = 0;

    // For inhalation, max Y is always 0 (zero at top)
    maxY = 0;

    // Create the chart
    return pw.Container(
      height: 300,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Padding(
        padding: const pw.EdgeInsets.all(16),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Courbe Débit/Volume (Inspiration)',
              style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 8),
            pw.Expanded(
              child: pw.Chart(
                grid: pw.CartesianGrid(
                  xAxis: pw.FixedAxis([
                    minX,
                    maxX / 4,
                    maxX / 2,
                    maxX * 3 / 4,
                    maxX,
                  ]),
                  yAxis: pw.FixedAxis([
                    minY,
                    minY * 3 / 4,
                    minY / 2,
                    minY / 4,
                    0,
                  ]),
                ),
                datasets: [
                  pw.LineDataSet(
                    legend: 'Débit/Volume',
                    drawPoints: false,
                    isCurved: true,
                    color: PdfColors.blue,
                    data: spots,
                  ),
                ],
                overlay: pw.Container(),
              ),
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Volume (L)', style: const pw.TextStyle(fontSize: 10)),
                pw.Text('Débit (L/s)', style: const pw.TextStyle(fontSize: 10)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build a table cell
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(fontWeight: isHeader ? pw.FontWeight.bold : null),
      ),
    );
  }

  // Save PDF to a file or share it
  Future<String?> savePdf(Uint8List pdfBytes) async {
    try {
      // Generate default filename with date and time
      final now = DateTime.now();
      final formattedDate =
          '${now.day}-${now.month}-${now.year}_${now.hour}-${now.minute}';
      final defaultFileName = 'rapport_test_respiratoire_$formattedDate.pdf';

      // Request appropriate permissions based on platform and Android version
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+)
        if (await Permission.photos.request().isGranted) {
          debugPrint('Photos permission granted');
        } else {
          // Try storage permission for older Android versions
          var storageStatus = await Permission.storage.request();
          if (!storageStatus.isGranted) {
            debugPrint('Storage permission denied');
            // Continue anyway as we'll use the share functionality which doesn't require permissions
          }
        }
      }

      // Get a temporary directory to store the file
      final tempDir = await getTemporaryDirectory();
      final tempFilePath = '${tempDir.path}/$defaultFileName';
      final tempFile = File(tempFilePath);
      await tempFile.writeAsBytes(pdfBytes);

      // Share the file
      final result = await Share.shareXFiles(
        [XFile(tempFilePath)],
        subject: 'Rapport de Test Respiratoire',
        text:
            'Rapport de Test Respiratoire généré le ${now.day}/${now.month}/${now.year}',
      );

      // If the user shared the file, consider it a success
      if (result.status == ShareResultStatus.success ||
          result.status == ShareResultStatus.dismissed) {
        return tempFilePath;
      } else {
        return null;
      }
    } catch (e) {
      debugPrint('Error saving PDF: $e');
      return null;
    }
  }

  // Preview and print PDF
  Future<void> printPdf(Uint8List pdfBytes) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfBytes,
    );
  }
}
