import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import 'package:befine/theme/app_theme.dart';
import 'package:befine/services/auth_service.dart';
import 'package:befine/routes/app_routes.dart';

class SignUpPatientScreen extends StatefulWidget {
  const SignUpPatientScreen({super.key});

  @override
  State<SignUpPatientScreen> createState() => _SignUpPatientScreenState();
}

class _SignUpPatientScreenState extends State<SignUpPatientScreen> {
  bool _isMale = true;
  final _formKey = GlobalKey<FormState>();

  // Essential Information
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _telController = TextEditingController();
  final _birthdayController = TextEditingController();
  String? _selectedGender;

  // Medical Information (Optional)
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _sosTelController = TextEditingController();
  final _notesController = TextEditingController();
  String? _selectedSmokingStatus;
  String? _selectedActivityLevel;
  String? _selectedBloodType;

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _showOptionalFields = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _telController.dispose();
    _birthdayController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _sosTelController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _handleSignUp() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);

      final authService = Provider.of<AuthService>(context, listen: false);

      try {
        // Validate height and weight as decimal(5,2)
        final height = double.tryParse(_heightController.text);
        final weight = double.tryParse(_weightController.text);
        if (height == null || height <= 0 || height >= 1000) {
          throw Exception('Valeur de taille invalide');
        }
        if (weight == null || weight <= 0 || weight >= 1000) {
          throw Exception('Valeur de poids invalide');
        }

        // Phone number is now required and validated by form validator
        if (_sosTelController.text.isNotEmpty &&
            !_isValidPhoneNumber(_sosTelController.text)) {
          throw Exception('Format de numéro d\'urgence invalide');
        }

        // Validate birthday
        if (!_isValidBirthday(_birthdayController.text)) {
          throw Exception('Format de date de naissance invalide (AAAA-MM-JJ)');
        }

        // Set default values for dropdowns if they're null (convert French to English for database)
        final bloodType =
            _selectedBloodType == 'Inconnu' || _selectedBloodType == null
                ? 'Unknown'
                : _selectedBloodType!;
        final smokingStatus =
            _selectedSmokingStatus == 'Inconnu' ||
                    _selectedSmokingStatus == null
                ? 'Unknown'
                : _selectedSmokingStatus == 'Jamais fumé'
                ? 'Never smoked'
                : _selectedSmokingStatus == 'Ancien fumeur'
                ? 'Former smoker'
                : _selectedSmokingStatus == 'Fumeur actuel'
                ? 'Current Smoker'
                : _selectedSmokingStatus == 'Fumeur passif'
                ? 'Passive Smoker'
                : _selectedSmokingStatus!;
        final activityLevel =
            _selectedActivityLevel == 'Inconnu' ||
                    _selectedActivityLevel == null
                ? 'Unknown'
                : _selectedActivityLevel == 'Inactif'
                ? 'Inactive'
                : _selectedActivityLevel == 'Légèrement actif'
                ? 'Lightly Active'
                : _selectedActivityLevel == 'Modérément actif'
                ? 'Moderately Active'
                : _selectedActivityLevel == 'Très actif'
                ? 'Very Active'
                : _selectedActivityLevel!;

        // Convert phone numbers to integers
        final phoneNumber = int.parse(
          _telController.text.trim().replaceAll(RegExp(r'[^\d]'), ''),
        );
        final emergencyContact =
            _sosTelController.text.trim().isNotEmpty
                ? int.parse(
                  _sosTelController.text.trim().replaceAll(
                    RegExp(r'[^\d]'),
                    '',
                  ),
                )
                : null;

        // Sign up patient using AuthService
        final success = await authService.signUpPatient(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          gender: _selectedGender ?? 'male',
          dateOfBirth: _birthdayController.text,
          height: height,
          weight: weight,
          phoneNumber: phoneNumber,
          smokingStatus: smokingStatus,
          bloodType: bloodType,
          activityLevel: activityLevel,
          emergencyContact: emergencyContact,
          notes: _notesController.text.trim(),
          address: 'Unknown', // Default address
        );

        if (mounted) {
          if (success) {
            // Navigate to patient main page
            AppRoutes.navigateToPatientMain(context);
          } else {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  authService.errorMessage ?? 'Échec de l\'inscription',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString()), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  bool _isValidPhoneNumber(String phone) {
    // Remove all non-digit characters
    String cleanNumber = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's exactly 8 digits
    return cleanNumber.length == 8 && int.tryParse(cleanNumber) != null;
  }

  bool _isValidBirthday(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      final now = DateTime.now();
      return parsedDate.isBefore(now) && parsedDate.year > 1900;
    } catch (e) {
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize dropdown values as null to show hints
    _selectedGender = 'male';
    _selectedSmokingStatus = null;
    _selectedActivityLevel = null;
    _selectedBloodType = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          physics: const BouncingScrollPhysics(),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                _buildEssentialInformation(),
                _buildOptionalInformationToggle(),
                if (_showOptionalFields) _buildMedicalInformation(),
                _buildSignUpButton(),
                _buildSignInLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const SizedBox(height: 5),
        Center(
          child: Image.asset(
            'assets/images/animation_checkup.gif',
            height: 150,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          'Créer un Compte Patient',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Veuillez remplir les informations essentielles pour commencer',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildEssentialInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Informations Obligatoires', Icons.person_outline),
        const SizedBox(height: 16),
        _buildGenderToggle(),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _firstNameController,
                label: 'Prénom *',
                icon: Icons.person_outline,
                required: true,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _lastNameController,
                label: 'Nom *',
                icon: Icons.person_outline,
                required: true,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),
        _buildDatePicker(),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _heightController,
                label: 'Taille (cm) *',
                icon: Icons.height,
                keyboardType: TextInputType.number,
                required: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'La taille est obligatoire';
                  }
                  final height = double.tryParse(value);
                  if (height == null || height <= 0 || height >= 300) {
                    return 'Entrez une taille valide (50-300 cm)';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _weightController,
                label: 'Poids (kg) *',
                icon: Icons.monitor_weight,
                keyboardType: TextInputType.number,
                required: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Le poids est obligatoire';
                  }
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0 || weight >= 500) {
                    return 'Entrez un poids valide (10-500 kg)';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _telController,
          label: 'Numéro de Téléphone *',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          required: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Le numéro de téléphone est obligatoire';
            }
            if (!_isValidPhoneNumber(value)) {
              return 'Entrez un numéro de téléphone valide (8 chiffres)';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
        _buildSectionHeader('Informations de Compte', Icons.lock_outline),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _emailController,
          label: 'Adresse Email *',
          icon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          required: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'L\'email est obligatoire';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Entrez une adresse email valide';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildPasswordField(),
        const SizedBox(height: 16),
        _buildConfirmPasswordField(),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildOptionalInformationToggle() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              _showOptionalFields = !_showOptionalFields;
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _showOptionalFields
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _showOptionalFields
                      ? 'Masquer les Informations Optionnelles'
                      : 'Ajouter les Informations Optionnelles',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMedicalInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          'Informations Optionnelles',
          Icons.medical_services_outlined,
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedBloodType,
          label: 'Groupe Sanguin',
          icon: Icons.bloodtype,
          items: const [
            'Inconnu',
            'A+',
            'A-',
            'B+',
            'B-',
            'AB+',
            'AB-',
            'O+',
            'O-',
          ],
          onChanged: (value) => setState(() => _selectedBloodType = value),
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedSmokingStatus,
          label: 'Statut Tabagique',
          icon: Icons.smoking_rooms,
          items: const [
            'Inconnu',
            'Jamais fumé',
            'Ancien fumeur',
            'Fumeur actuel',
            'Fumeur passif',
          ],
          onChanged: (value) => setState(() => _selectedSmokingStatus = value),
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedActivityLevel,
          label: 'Niveau d\'Activité',
          icon: Icons.directions_run,
          items: const [
            'Inconnu',
            'Inactif',
            'Légèrement actif',
            'Modérément actif',
            'Très actif',
          ],
          onChanged: (value) => setState(() => _selectedActivityLevel = value),
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _sosTelController,
          label: 'Contact d\'Urgence',
          icon: Icons.emergency,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value?.isEmpty ?? true) return null;
            return !_isValidPhoneNumber(value!)
                ? 'Entrez un numéro de téléphone valide (8 chiffres)'
                : null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _notesController,
          label: 'Notes Médicales',
          icon: Icons.note,
          maxLines: 3,
          hintText: 'Entrez toute allergie, condition ou note importante',
        ),
      ],
    );
  }

  Widget _buildSignUpButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSignUp,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Text('Créer un Compte'),
      ),
    );
  }

  Widget _buildSignInLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Vous avez déjà un compte ? ',
          style: TextStyle(color: AppTheme.textSecondaryColor),
        ),
        TextButton(
          onPressed: () {
            // Navigate back to sign-in screen while preserving the selected role
            Navigator.of(context).pushReplacementNamed('/sign-in');
          },
          style: TextButton.styleFrom(foregroundColor: AppTheme.primaryColor),
          child: const Text('Se Connecter'),
        ),
      ],
    );
  }

  // Helper Widgets
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 24),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    bool required = false,
    int maxLines = 1,
    String? hintText,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: required ? '$label*' : label,
        hintText: hintText,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      validator:
          required
              ? (value) =>
                  value?.isEmpty ?? true
                      ? '$label est obligatoire'
                      : validator?.call(value)
              : validator,
    );
  }

  Widget _buildDropdownField({
    required String? value,
    required String label,
    required IconData icon,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    // Show hint if value is "Unknown"
    final displayValue = value == "Unknown" ? null : value;

    return DropdownButtonFormField<String>(
      value: displayValue,
      dropdownColor: Colors.white,
      icon: const Icon(Icons.arrow_drop_down),
      elevation: 8,
      isExpanded: true,
      style: const TextStyle(fontSize: 16, color: Colors.black87),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      menuMaxHeight: 300,
      borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
      items:
          items
              .map(
                (item) => DropdownMenuItem<String>(
                  value: item,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(item),
                  ),
                ),
              )
              .toList(),
      onChanged: onChanged,
      hint: Text(
        'Sélectionner $label',
        style: TextStyle(color: Colors.grey.shade600),
      ),
    );
  }

  Widget _buildDatePicker() {
    return TextFormField(
      controller: _birthdayController,
      decoration: InputDecoration(
        labelText: 'Date de Naissance *',
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      readOnly: true,
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: DateTime.now().subtract(
            const Duration(days: 6570),
          ), // 18 years ago
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _birthdayController.text = DateFormat('yyyy-MM-dd').format(date);
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'La date de naissance est obligatoire';
        }
        if (!_isValidBirthday(value)) {
          return 'Veuillez entrer une date valide';
        }
        return null;
      },
    );
  }

  Widget _buildGenderToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _isMale = true),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(11),
                  color: _isMale ? AppTheme.primaryColor : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.male_rounded,
                      color: _isMale ? Colors.white : Colors.grey,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Homme',
                      style: TextStyle(
                        color: _isMale ? Colors.white : Colors.grey,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _isMale = false),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(11),
                  color: !_isMale ? AppTheme.primaryColor : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.female_rounded,
                      color: !_isMale ? Colors.white : Colors.grey,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Femme',
                      style: TextStyle(
                        color: !_isMale ? Colors.white : Colors.grey,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Mot de Passe *',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Le mot de passe est obligatoire';
        }
        if (value.length < 6) {
          return 'Le mot de passe doit contenir au moins 6 caractères';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Confirmer le Mot de Passe *',
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Veuillez confirmer votre mot de passe';
        }
        if (value != _passwordController.text) {
          return 'Les mots de passe ne correspondent pas';
        }
        return null;
      },
    );
  }
}
