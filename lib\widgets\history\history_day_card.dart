import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/history/history_day_detail.dart';

class HistoryDayCard extends StatelessWidget {
  final DateTime date;
  final bool isToday;
  final int activityCount;
  final VoidCallback onTap;

  const HistoryDayCard({
    Key? key,
    required this.date,
    required this.isToday,
    required this.activityCount,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Format day name (e.g., "Monday")
    final String dayName = _getDayName(date.weekday);

    // Format date (e.g., "Jan 15, 2025")
    final String formattedDate =
        "${_getMonthName(date.month)} ${date.day}, ${date.year}";

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HistoryDayDetail(date: date),
            ),
          );
        },
        borderRadius: BorderRadius.circular(15),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Date container with colored background
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isToday ? AppTheme.primaryColor : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      date.day.toString(),
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color:
                            isToday ? Colors.white : AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      dayName.substring(0, 3), // Abbreviated day name
                      style: TextStyle(
                        fontSize: 14,
                        color:
                            isToday
                                ? Colors.white
                                : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isToday
                          ? "Today, $formattedDate"
                          : "$dayName, $formattedDate",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "$activityCount activities recorded",
                      style: TextStyle(color: AppTheme.textSecondaryColor),
                    ),
                    const SizedBox(height: 8),
                    // Progress indicator
                    LinearProgressIndicator(
                      value: activityCount / 5, // 5 is max activities
                      backgroundColor: AppTheme.backgroundColor,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryColor,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.textTertiaryColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to get day name
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return "Monday";
      case 2:
        return "Tuesday";
      case 3:
        return "Wednesday";
      case 4:
        return "Thursday";
      case 5:
        return "Friday";
      case 6:
        return "Saturday";
      case 7:
        return "Sunday";
      default:
        return "";
    }
  }

  // Helper method to get month name
  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return "Jan";
      case 2:
        return "Feb";
      case 3:
        return "Mar";
      case 4:
        return "Apr";
      case 5:
        return "May";
      case 6:
        return "Jun";
      case 7:
        return "Jul";
      case 8:
        return "Aug";
      case 9:
        return "Sep";
      case 10:
        return "Oct";
      case 11:
        return "Nov";
      case 12:
        return "Dec";
      default:
        return "";
    }
  }
}
