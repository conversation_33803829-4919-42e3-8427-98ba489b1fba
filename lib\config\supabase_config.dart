import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase configuration class for BeFine application
class SupabaseConfig {
  // Supabase project configuration
  static const String supabaseUrl = 'https://gyigqurtteohgprngjjs.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd5aWdxdXJ0dGVvaGdwcm5nampzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1MzkxMjgsImV4cCI6MjA2NDExNTEyOH0.t-nxpcZcLRaq7NuvjtwOgSIKr42VPOWl70-7j74HVXM';

  /// Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce,
      ),
    );
  }

  /// Get Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;

  /// Get current user
  static User? get currentUser => client.auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;
}
