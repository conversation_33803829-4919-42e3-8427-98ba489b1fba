import 'package:flutter/material.dart';
import 'package:graph_app/models/measurement_data.dart';
import 'package:graph_app/models/test_results.dart';
import 'package:graph_app/services/ble_service.dart';
import 'package:graph_app/widgets/ble_connection_widget.dart';
import 'package:graph_app/widgets/expandable_chart_card.dart';
import 'package:graph_app/widgets/flow_chart.dart';
import 'package:graph_app/widgets/flow_volume_chart.dart';
import 'package:graph_app/widgets/test_results_card.dart';
import 'package:graph_app/widgets/volume_chart.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Courbe Débit/Volume'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Consumer<BleService>(
      builder: (context, bleService, child) {
        // Check if BLE is supported on this platform
        if (bleService.testStatus == "unsupported") {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.bluetooth_disabled,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Bluetooth Low Energy n\'est pas pris en charge sur cette plateforme',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Cette application nécessite un appareil mobile avec prise en charge Bluetooth Low Energy.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // Show demo data
                      // This could be implemented to show sample data for demonstration
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Mode démo non implémenté'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                    child: const Text('Afficher les données de démo'),
                  ),
                ],
              ),
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // BLE connection widget
              const BleConnectionWidget(),
              const SizedBox(height: 24),

              // Start measurement button
              if (bleService.connectionState == BleConnectionState.connected)
                _buildStartMeasurementButton(context, bleService),

              const SizedBox(height: 24),

              // Charts
              _buildCharts(bleService.measurementData),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStartMeasurementButton(
    BuildContext context,
    BleService bleService,
  ) {
    // Get test status
    final testStatus = bleService.testStatus;
    final isMeasuring = testStatus == "measuring";
    final isComplete = testStatus == "complete" || testStatus == "stopped";
    final progress = bleService.testProgress;
    final currentSample = bleService.currentSample;
    final maxSamples = bleService.maxSamples;

    return Column(
      children: [
        // Progress indicator
        if (isMeasuring)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              children: [
                LinearProgressIndicator(
                  value: progress / 100.0,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 10,
                  borderRadius: BorderRadius.circular(5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Échantillon: $currentSample / $maxSamples (${progress.toStringAsFixed(1)}%)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

        // Status indicator
        if (isComplete)
          Container(
            padding: const EdgeInsets.all(8.0),
            margin: const EdgeInsets.only(bottom: 16.0),
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withAlpha(75)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Test terminé avec $currentSample échantillons',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        // Control buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!isMeasuring)
              ElevatedButton.icon(
                onPressed: () => _startMeasurementCycle(context, bleService),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Démarrer'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              )
            else
              ElevatedButton.icon(
                onPressed: () => _stopMeasurementCycle(context, bleService),
                icon: const Icon(Icons.stop),
                label: const Text('Arrêter le test'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),

            if (isComplete)
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: ElevatedButton.icon(
                  onPressed: () => _resetMeasurementCycle(context, bleService),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Réinitialiser'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    textStyle: const TextStyle(fontSize: 18),
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCharts(MeasurementData measurementData) {
    // Get BLE service to check test status
    final bleService = Provider.of<BleService>(context, listen: false);
    final isTestComplete =
        bleService.testStatus == "complete" ||
        bleService.testStatus == "stopped";

    // Calculate test results if test is complete and we have data
    final hasData =
        measurementData.flowData.isNotEmpty &&
        measurementData.volumeData.isNotEmpty;
    final TestResults testResults =
        hasData ? measurementData.calculateResults() : TestResults.empty();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main Flow/Volume chart (always visible)
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FlowVolumeChart(
            flowData: measurementData.flowData,
            volumeData: measurementData.volumeData,
          ),
        ),

        // Test Results Card (only shown when test is complete)
        if (isTestComplete && hasData)
          Column(
            children: [
              const SizedBox(height: 16),
              TestResultsCard(
                results: testResults,
                measurementData: measurementData,
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Expandable Flow chart
        ExpandableChartCard(
          title: 'Courbe Débit',
          chart: FlowChart(flowData: measurementData.flowData),
        ),

        // Expandable Volume chart
        ExpandableChartCard(
          title: 'Courbe Volume',
          chart: VolumeChart(volumeData: measurementData.volumeData),
        ),
      ],
    );
  }

  void _startMeasurementCycle(
    BuildContext context,
    BleService bleService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleService.startMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec du démarrage du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test démarré'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _stopMeasurementCycle(
    BuildContext context,
    BleService bleService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleService.stopMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec de l\'arrêt du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test arrêté'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _resetMeasurementCycle(
    BuildContext context,
    BleService bleService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleService.resetMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec de la réinitialisation du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test réinitialisé avec succès'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }
}
