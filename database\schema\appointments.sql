-- Appointments table creation
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES patients(id),
    doctor_id UUID REFERENCES doctors(id),
    appointment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status text, -- 'scheduled', 'completed', 'cancelled'
    type text, -- 'check-up', 'emergency', 'follow-up'
    notes TEXT
);

-- Enable RLS for appointments table
--ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

-- Patient Monitoring table creation
CREATE TABLE patient_monitoring (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES patients(id),
    doctor_id UUID REFERENCES doctors(id),
    monitoring_type text, -- 'daily_check', 'emergency_alert', etc.
    status text,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS for patient_monitoring table
--ALTER TABLE patient_monitoring ENABLE ROW LEVEL SECURITY;

-- Create indexes for foreign keys
CREATE INDEX idx_patient_id_appointments ON appointments(patient_id);
CREATE INDEX idx_doctor_id_appointments ON appointments(doctor_id);
CREATE INDEX idx_patient_id_monitoring ON patient_monitoring(patient_id);
CREATE INDEX idx_doctor_id_monitoring ON patient_monitoring(doctor_id);