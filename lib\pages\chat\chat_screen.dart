import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:befine/services/openai_service.dart';
import 'package:befine/widgets/chat/chat_message_widget.dart';
import 'package:befine/widgets/chat/chat_input_widget.dart';
import 'package:befine/theme/app_theme.dart';

/// Main chat screen for medical conversations with ChatGPT
class ChatScreen extends StatefulWidget {
  const ChatScreen({Key? key}) : super(key: key);

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ScrollController _scrollController = ScrollController();
  late OpenAIService _openAIService;

  @override
  void initState() {
    super.initState();
    _openAIService = OpenAIService();

    // Listen to message changes to auto-scroll
    _openAIService.addListener(_scrollToBottom);
  }

  @override
  void dispose() {
    _openAIService.removeListener(_scrollToBottom);
    _openAIService.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Auto-scroll to bottom when new messages arrive
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  /// Handle sending messages
  void _handleSendMessage(String message) {
    _openAIService.sendMessage(message);
  }

  /// Show clear conversation dialog
  void _showClearDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Effacer la Conversation',
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            content: Text(
              'Êtes-vous sûr de vouloir effacer toute la conversation ? Cette action ne peut pas être annulée.',
              style: TextStyle(color: AppTheme.textSecondaryColor),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _openAIService.clearConversation();
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Effacer'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _openAIService,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            Expanded(child: _buildMessagesList()),
            Consumer<OpenAIService>(
              builder: (context, service, child) {
                return ChatInputWidget(
                  onSendMessage: _handleSendMessage,
                  isLoading: service.isLoading,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.smart_toy_outlined,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Assistant Médical BeFine',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Support santé alimenté par IA',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _showClearDialog,
          icon: const Icon(Icons.delete_outline),
          tooltip: 'Effacer la conversation',
        ),
      ],
    );
  }

  /// Build messages list
  Widget _buildMessagesList() {
    return Consumer<OpenAIService>(
      builder: (context, service, child) {
        if (service.messages.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(vertical: 16),
          itemCount: service.messages.length,
          itemBuilder: (context, index) {
            final message = service.messages[index];
            final showTimestamp =
                index == 0 ||
                (index > 0 &&
                    message.timestamp
                            .difference(service.messages[index - 1].timestamp)
                            .inMinutes >
                        5);

            return ChatMessageWidget(
              message: message,
              showTimestamp: showTimestamp,
            );
          },
        );
      },
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.smart_toy_rounded,
                size: 40,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Bienvenue dans l\'Assistant Médical',
              style: TextStyle(
                color: AppTheme.textPrimaryColor,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Posez-moi toutes vos questions sur votre santé respiratoire, l\'utilisation des inhalateurs ou des questions médicales générales.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: 16,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
