import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/models/spray_model.dart';
import 'package:befine/routes/app_routes.dart';

class DeviceWidget extends StatefulWidget {
  const DeviceWidget({Key? key}) : super(key: key);

  @override
  State<DeviceWidget> createState() => _DeviceWidgetState();
}

class _DeviceWidgetState extends State<DeviceWidget> {
  // Demo spray data - in a real app this would come from a service
  final SprayModel _currentSpray = SprayModel.demoSprays.first;
  final int _leftDoses = 85; // Demo value for left doses

  @override
  Widget build(BuildContext context) {
    return Consumer2<DeviceService, BleManager>(
      builder: (context, deviceService, bleManager, child) {
        // Get device information
        final deviceInfo = _getDeviceInfo(deviceService, bleManager);

        return GestureDetector(
          onTap: () => _navigateToDeviceInfo(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        deviceInfo['name'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          overflow: TextOverflow.fade,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color:
                            deviceInfo['isConnected']
                                ? Colors.green
                                : Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => _navigateToManageDevices(context),
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.settings,
                          color: Theme.of(context).colorScheme.onPrimary,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      _getBatteryIcon(deviceInfo['batteryLevel']),
                      size: 20,
                      color: _getBatteryColor(deviceInfo['batteryLevel']),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${deviceInfo['batteryLevel']}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).hintColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Center(
                    child: Image.asset(
                      'assets/images/healer.png',
                      height: 120,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.surface.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _currentSpray
                                .doseType
                                .displayName, // Display actual dose type
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(
                            Icons.medication,
                            size: 18,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '$_leftDoses doses left', // Display actual left doses
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Get device information from services
  Map<String, dynamic> _getDeviceInfo(
    DeviceService deviceService,
    BleManager bleManager,
  ) {
    // Check if there's a connected device via BLE
    if (bleManager.connectionState == BleConnectionState.connected &&
        bleManager.connectedDevice != null) {
      final device = bleManager.connectedDevice!;
      final deviceId = device.remoteId.toString();

      // Get saved device info
      final savedDevice = deviceService.getDeviceById(deviceId);

      return {
        'name':
            savedDevice?.customName ??
            (device.platformName.isNotEmpty
                ? device.platformName
                : 'SmartInhealer'),
        'isConnected': true,
        'batteryLevel':
            savedDevice?.batteryLevel ?? 95, // Default if not available
      };
    }

    // Check if there are any saved devices
    final devices = deviceService.devices;
    if (devices.isNotEmpty) {
      // Get the last connected device or first device
      final device =
          deviceService.lastConnectedDeviceId != null
              ? deviceService.getDeviceById(
                deviceService.lastConnectedDeviceId!,
              )
              : devices.first;

      if (device != null) {
        return {
          'name': device.customName ?? device.deviceType ?? 'SmartInhealer',
          'isConnected': device.isConnected ?? false,
          'batteryLevel': device.batteryLevel ?? 0,
        };
      }
    }

    // Default device info when no devices are available
    return {'name': 'No Device', 'isConnected': false, 'batteryLevel': 0};
  }

  /// Navigate to device info page
  void _navigateToDeviceInfo(BuildContext context) {
    Navigator.pushNamed(context, AppRoutes.deviceInfo);
  }

  /// Navigate to manage devices screen
  void _navigateToManageDevices(BuildContext context) {
    Navigator.pushNamed(context, AppRoutes.manageDevices);
  }

  /// Get battery icon based on level
  IconData _getBatteryIcon(int batteryLevel) {
    if (batteryLevel >= 90) {
      return Icons.battery_full;
    } else if (batteryLevel >= 60) {
      return Icons.battery_5_bar;
    } else if (batteryLevel >= 40) {
      return Icons.battery_3_bar;
    } else if (batteryLevel >= 20) {
      return Icons.battery_2_bar;
    } else if (batteryLevel > 0) {
      return Icons.battery_1_bar;
    } else {
      return Icons.battery_0_bar;
    }
  }

  /// Get battery color based on level
  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel >= 50) {
      return Colors.green;
    } else if (batteryLevel >= 20) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
