-- Devices table creation
CREATE TABLE devices (
    id bigint primary key generated always as identity,
    patient_id UUID REFERENCES patients(id),
    device_name text NOT NULL,
    mac_address text UNIQUE NOT NULL,
    battery_level INTEGER,
    last_sync TIMESTAMP,
    status BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) --WITH (OIDS=FALSE)
;

-- Enable RLS for devices table
--ALTER TABLE devices ENABLE ROW LEVEL SECURITY;

-- Device Readings table creation
CREATE TABLE device_readings (
    id UUID PRIMARY KEY,
    device_id bigint REFERENCES devices(id),
    co_level DOUBLE ,
    volume_temp DECIMAL(2,12)[] NOT NULL, -- Array of decimal values
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) --WITH (OIDS=FALSE)
;

-- Enable RLS for device_readings table
--ALTER TABLE device_readings ENABLE ROW LEVEL SECURITY;

-- Create index for foreign key on device_id
CREATE INDEX idx_device_id ON device_readings(device_id);