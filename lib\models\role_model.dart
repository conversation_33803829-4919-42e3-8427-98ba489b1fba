class RoleModel {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  RoleModel({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
  });

  static List<RoleModel> roles = [
    RoleModel(
      id: 'patient',
      title: 'Patient',
      description: 'Suivez vos métriques de santé et gérez votre condition.',
      iconPath: 'assets/images/animation.patient_phone.gif',
    ),
    RoleModel(
      id: 'doctor',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description:
          'Surveillez les patients et fournissez des conseils médicaux.',
      iconPath: 'assets/images/animation_doctor.gif',
    ),
  ];
}
