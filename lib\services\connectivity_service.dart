import 'dart:async';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

/// Service for monitoring network connectivity and managing offline/online states
class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = true;
  bool _isInitialized = false;
  ConnectivityResult _connectionType = ConnectivityResult.none;

  // Getters
  bool get isOnline => _isOnline;
  bool get isOffline => !_isOnline;
  bool get isInitialized => _isInitialized;
  ConnectivityResult get connectionType => _connectionType;

  /// Initialize the connectivity service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      await _checkInitialConnectivity();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          debugPrint('Connectivity stream error: $error');
        },
      );

      _isInitialized = true;
      debugPrint('ConnectivityService initialized - isOnline: $_isOnline');
    } catch (e) {
      debugPrint('Error initializing ConnectivityService: $e');
      // Assume offline if initialization fails
      _isOnline = false;
      _isInitialized = true;
    }
  }

  /// Check initial connectivity status
  Future<void> _checkInitialConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      await _updateConnectivityStatus(results);
    } catch (e) {
      debugPrint('Error checking initial connectivity: $e');
      _isOnline = false;
      _connectionType = ConnectivityResult.none;
    }
  }

  /// Handle connectivity changes
  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    await _updateConnectivityStatus(results);
  }

  /// Update connectivity status based on connectivity results
  Future<void> _updateConnectivityStatus(List<ConnectivityResult> results) async {
    if (results.isEmpty) {
      _connectionType = ConnectivityResult.none;
      _isOnline = false;
    } else {
      // Take the first non-none result
      _connectionType = results.firstWhere(
        (result) => result != ConnectivityResult.none,
        orElse: () => ConnectivityResult.none,
      );

      // If we have a connection type, verify with actual internet connectivity
      if (_connectionType != ConnectivityResult.none) {
        _isOnline = await _verifyInternetConnectivity();
      } else {
        _isOnline = false;
      }
    }

    debugPrint('Connectivity changed - Type: $_connectionType, Online: $_isOnline');
    notifyListeners();
  }

  /// Verify actual internet connectivity by making a test request
  Future<bool> _verifyInternetConnectivity() async {
    try {
      final response = await http
          .get(
            Uri.parse('https://www.google.com'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Internet connectivity verification failed: $e');
      return false;
    }
  }

  /// Force check connectivity status
  Future<bool> checkConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      await _updateConnectivityStatus(results);
      return _isOnline;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return false;
    }
  }

  /// Wait for internet connection to be available
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isOnline) return true;

    final completer = Completer<bool>();
    late StreamSubscription subscription;

    // Set up timeout
    final timer = Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    // Listen for connectivity changes
    subscription = _connectivity.onConnectivityChanged.listen((results) async {
      await _updateConnectivityStatus(results);
      if (_isOnline && !completer.isCompleted) {
        completer.complete(true);
      }
    });

    final result = await completer.future;
    
    // Cleanup
    timer.cancel();
    await subscription.cancel();
    
    return result;
  }

  /// Dispose resources
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
