import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';

class PremiumFeatures extends StatelessWidget {
  final List<Map<String, dynamic>> features;

  const PremiumFeatures({Key? key, required this.features}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Premium Features',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
          ),
          const SizedBox(height: 15),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: AppTheme.surfaceColor,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  ...features.map((feature) => _buildFeatureItem(
                        context,
                        feature['icon'] as IconData,
                        feature['title'] as String,
                        feature['description'] as String,
                        feature['enabled'] as bool,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    bool enabled,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: enabled
                ? AppTheme.primaryColor.withOpacity(0.1)
                : Colors.grey.withOpacity(0.1),
            child: Icon(
              icon,
              color: enabled ? AppTheme.primaryColor : Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: enabled ? AppTheme.textPrimaryColor : Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: enabled ? AppTheme.textSecondaryColor : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          // Removed the Switch widget
          if (enabled)
            Icon(
              Icons.check_circle,
              color: AppTheme.primaryColor,
              size: 20,
            )
        ],
      ),
    );
  }
}