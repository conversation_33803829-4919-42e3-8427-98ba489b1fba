import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:befine/services/auth_service.dart';
import 'package:befine/services/connectivity_service.dart';
import 'package:befine/pages/welcom/onboarding_screen.dart';
import 'package:befine/pages/patient/patient_main_page.dart';
import 'package:befine/pages/doctor/doctor_main_page.dart';
import 'package:befine/theme/app_theme.dart';

/// Authentication wrapper that handles auto-login and routing
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final connectivityService = Provider.of<ConnectivityService>(
      context,
      listen: false,
    );

    try {
      // Initialize connectivity service first
      await connectivityService.initialize();

      debugPrint(
        'Connectivity initialized - isOnline: ${connectivityService.isOnline}',
      );

      // Check if user should stay logged in
      final prefs = await SharedPreferences.getInstance();
      final keepLoggedIn = prefs.getBool('keepLoggedIn') ?? false;

      debugPrint('Auth initialization - keepLoggedIn: $keepLoggedIn');

      if (keepLoggedIn) {
        // User chose to stay logged in, try offline authentication first
        debugPrint(
          'User chose to stay logged in, checking offline authentication...',
        );

        final shouldAuthOffline = await authService.shouldAuthenticateOffline();

        if (shouldAuthOffline) {
          debugPrint('Attempting offline authentication...');
          final offlineSuccess =
              await authService.initializeWithOfflineSupport();

          if (offlineSuccess) {
            debugPrint('Offline authentication successful');

            // If online, update last online time
            if (connectivityService.isOnline) {
              await authService.persistenceService.updateLastOnlineTime();
            }

            return; // Successfully authenticated offline
          } else {
            debugPrint('Offline authentication failed');
          }
        }

        // If offline auth failed or not available, try online auth if connected
        if (connectivityService.isOnline) {
          debugPrint('Attempting online authentication...');
          await authService.initialize();

          if (authService.isAuthenticated) {
            debugPrint('Online authentication successful');
            return;
          } else {
            debugPrint(
              'Online authentication failed, user needs to sign in again',
            );
          }
        } else {
          debugPrint(
            'No internet connection and offline authentication failed',
          );
        }
      } else {
        // User didn't choose to stay logged in
        debugPrint('User did not choose to stay logged in');

        if (connectivityService.isOnline) {
          // Check if there's an active session and sign out if needed
          await authService.initialize();
          if (authService.isAuthenticated) {
            debugPrint(
              'Found active session but user didn\'t choose to stay logged in, signing out...',
            );
            await authService.signOut();
          }
        }
      }
    } catch (e) {
      debugPrint('Auth initialization error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return const Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Initializing...',
                style: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer2<AuthService, ConnectivityService>(
      builder: (context, authService, connectivityService, child) {
        if (authService.isLoading) {
          return Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  connectivityService.isOffline
                      ? 'Chargement en mode hors ligne...'
                      : 'Chargement...',
                  style: const TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 16,
                  ),
                ),
                if (connectivityService.isOffline)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.wifi_off, size: 16, color: Colors.orange),
                        SizedBox(width: 4),
                        Text(
                          'Mode hors ligne',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }

        if (authService.isAuthenticated) {
          if (authService.isPatient) {
            return const PatientMainPage();
          } else if (authService.isDoctor) {
            return const DoctorMainPage();
          }
        }

        // Not authenticated, show onboarding
        return const OnboardingScreen();
      },
    );
  }
}
