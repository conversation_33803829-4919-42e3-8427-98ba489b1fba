import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:befine/services/auth_service.dart';
import 'package:befine/pages/welcom/onboarding_screen.dart';
import 'package:befine/pages/patient/patient_main_page.dart';
import 'package:befine/pages/doctor/doctor_main_page.dart';
import 'package:befine/theme/app_theme.dart';

/// Authentication wrapper that handles auto-login and routing
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final authService = Provider.of<AuthService>(context, listen: false);

    try {
      // Initialize auth service
      await authService.initialize();

      // Check if user should stay logged in
      final prefs = await SharedPreferences.getInstance();
      final keepLoggedIn = prefs.getBool('keepLoggedIn') ?? false;

      debugPrint(
        'Auth initialization - keepLoggedIn: $keepLoggedIn, isAuthenticated: ${authService.isAuthenticated}',
      );

      // If user is authenticated but didn't choose to stay logged in, sign them out
      if (authService.isAuthenticated && !keepLoggedIn) {
        debugPrint('User did not choose to stay logged in, signing out...');
        await authService.signOut();
      } else if (authService.isAuthenticated && keepLoggedIn) {
        debugPrint('User chose to stay logged in, keeping session active');
      } else if (!authService.isAuthenticated) {
        debugPrint('User is not authenticated');
      }
    } catch (e) {
      debugPrint('Auth initialization error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return const Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Initializing...',
                style: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<AuthService>(
      builder: (context, authService, child) {
        if (authService.isLoading) {
          return const Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
            ),
          );
        }

        if (authService.isAuthenticated) {
          if (authService.isPatient) {
            return const PatientMainPage();
          } else if (authService.isDoctor) {
            return const DoctorMainPage();
          }
        }

        // Not authenticated, show onboarding
        return const OnboardingScreen();
      },
    );
  }
}
