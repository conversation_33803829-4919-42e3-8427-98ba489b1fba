import 'dart:typed_data';

/// Enum representing the possible dose synchronization statuses
enum DoseSyncStatus {
  quick,
  late,
  correct,
  unknown
}

/// Enum representing the types of doses that can be used
enum DoseType {
  reliever,
  controller,
  combination,
  other,
  unknown
}

/// Model class for storing and managing data metrics collected by the SmartHealer device
class DataMetrics {
  /// Unique identifier for the data record
  final String id;
  
  /// Patient ID associated with this data
  final String patientId;
  
  /// Device ID that collected this data
  final String deviceId;
  
  /// Timestamp when the data was collected
  final DateTime timestamp;
  
  /// CO compression levels in exhaled breath (ppm)
  final double coLevel;
  
  /// Peak Expiratory Flow in Liters/minute
  final double pef;
  
  /// Forced Exhaled Volume in first second (Liters)
  final double fev1;
  
  /// Raw data buffer of the flow measurements
  final Uint8List? flowData;
  
  /// Status of dose synchronization
  final DoseSyncStatus doseSyncStatus;
  
  /// Type of dose used
  final DoseType doseType;
  
  /// Battery level of the device (percentage)
  final int batteryLevel;
  
  /// Forced Vital Capacity (Liters)
  final double? fvc;
  
  /// FEV1/FVC ratio (percentage)
  final double? fev1FvcRatio;
  
  /// Forced Expiratory Flow 25-75% (Liters/second)
  final double? fef2575;
  
  /// Respiratory rate (breaths per minute)
  final int? respiratoryRate;
  
  /// Temperature of exhaled breath (Celsius)
  final double? breathTemperature;
  
  /// Humidity of exhaled breath (percentage)
  final double? breathHumidity;
  
  /// Ambient temperature (Celsius)
  final double? ambientTemperature;
  
  /// Ambient humidity (percentage)
  final double? ambientHumidity;
  
  /// Ambient pressure (hPa)
  final double? ambientPressure;
  
  /// Number of inhalations detected
  final int? inhalationCount;
  
  /// Duration of inhalation (milliseconds)
  final int? inhalationDuration;
  
  /// Whether the technique was correct
  final bool? correctTechnique;
  
  /// Notes or comments about this measurement
  final String? notes;
  
  /// Additional metadata as key-value pairs
  final Map<String, dynamic>? metadata;

  DataMetrics({
    required this.id,
    required this.patientId,
    required this.deviceId,
    required this.timestamp,
    required this.coLevel,
    required this.pef,
    required this.fev1,
    this.flowData,
    required this.doseSyncStatus,
    required this.doseType,
    required this.batteryLevel,
    this.fvc,
    this.fev1FvcRatio,
    this.fef2575,
    this.respiratoryRate,
    this.breathTemperature,
    this.breathHumidity,
    this.ambientTemperature,
    this.ambientHumidity,
    this.ambientPressure,
    this.inhalationCount,
    this.inhalationDuration,
    this.correctTechnique,
    this.notes,
    this.metadata,
  });

  /// Create a DataMetrics object from a JSON map
  factory DataMetrics.fromJson(Map<String, dynamic> json) {
    return DataMetrics(
      id: json['id'],
      patientId: json['patientId'],
      deviceId: json['deviceId'],
      timestamp: DateTime.parse(json['timestamp']),
      coLevel: json['coLevel'].toDouble(),
      pef: json['pef'].toDouble(),
      fev1: json['fev1'].toDouble(),
      flowData: json['flowData'] != null 
          ? Uint8List.fromList(List<int>.from(json['flowData']))
          : null,
      doseSyncStatus: _parseDoseSyncStatus(json['doseSyncStatus']),
      doseType: _parseDoseType(json['doseType']),
      batteryLevel: json['batteryLevel'],
      fvc: json['fvc']?.toDouble(),
      fev1FvcRatio: json['fev1FvcRatio']?.toDouble(),
      fef2575: json['fef2575']?.toDouble(),
      respiratoryRate: json['respiratoryRate'],
      breathTemperature: json['breathTemperature']?.toDouble(),
      breathHumidity: json['breathHumidity']?.toDouble(),
      ambientTemperature: json['ambientTemperature']?.toDouble(),
      ambientHumidity: json['ambientHumidity']?.toDouble(),
      ambientPressure: json['ambientPressure']?.toDouble(),
      inhalationCount: json['inhalationCount'],
      inhalationDuration: json['inhalationDuration'],
      correctTechnique: json['correctTechnique'],
      notes: json['notes'],
      metadata: json['metadata'],
    );
  }

  /// Convert the DataMetrics object to a JSON map
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'patientId': patientId,
      'deviceId': deviceId,
      'timestamp': timestamp.toIso8601String(),
      'coLevel': coLevel,
      'pef': pef,
      'fev1': fev1,
      'doseSyncStatus': doseSyncStatus.toString().split('.').last,
      'doseType': doseType.toString().split('.').last,
      'batteryLevel': batteryLevel,
    };

    // Add optional fields if they are not null
    if (flowData != null) data['flowData'] = flowData!.toList();
    if (fvc != null) data['fvc'] = fvc;
    if (fev1FvcRatio != null) data['fev1FvcRatio'] = fev1FvcRatio;
    if (fef2575 != null) data['fef2575'] = fef2575;
    if (respiratoryRate != null) data['respiratoryRate'] = respiratoryRate;
    if (breathTemperature != null) data['breathTemperature'] = breathTemperature;
    if (breathHumidity != null) data['breathHumidity'] = breathHumidity;
    if (ambientTemperature != null) data['ambientTemperature'] = ambientTemperature;
    if (ambientHumidity != null) data['ambientHumidity'] = ambientHumidity;
    if (ambientPressure != null) data['ambientPressure'] = ambientPressure;
    if (inhalationCount != null) data['inhalationCount'] = inhalationCount;
    if (inhalationDuration != null) data['inhalationDuration'] = inhalationDuration;
    if (correctTechnique != null) data['correctTechnique'] = correctTechnique;
    if (notes != null) data['notes'] = notes;
    if (metadata != null) data['metadata'] = metadata;

    return data;
  }

  /// Create a copy of this DataMetrics with the given fields replaced with new values
  DataMetrics copyWith({
    String? id,
    String? patientId,
    String? deviceId,
    DateTime? timestamp,
    double? coLevel,
    double? pef,
    double? fev1,
    Uint8List? flowData,
    DoseSyncStatus? doseSyncStatus,
    DoseType? doseType,
    int? batteryLevel,
    double? fvc,
    double? fev1FvcRatio,
    double? fef2575,
    int? respiratoryRate,
    double? breathTemperature,
    double? breathHumidity,
    double? ambientTemperature,
    double? ambientHumidity,
    double? ambientPressure,
    int? inhalationCount,
    int? inhalationDuration,
    bool? correctTechnique,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return DataMetrics(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      deviceId: deviceId ?? this.deviceId,
      timestamp: timestamp ?? this.timestamp,
      coLevel: coLevel ?? this.coLevel,
      pef: pef ?? this.pef,
      fev1: fev1 ?? this.fev1,
      flowData: flowData ?? this.flowData,
      doseSyncStatus: doseSyncStatus ?? this.doseSyncStatus,
      doseType: doseType ?? this.doseType,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      fvc: fvc ?? this.fvc,
      fev1FvcRatio: fev1FvcRatio ?? this.fev1FvcRatio,
      fef2575: fef2575 ?? this.fef2575,
      respiratoryRate: respiratoryRate ?? this.respiratoryRate,
      breathTemperature: breathTemperature ?? this.breathTemperature,
      breathHumidity: breathHumidity ?? this.breathHumidity,
      ambientTemperature: ambientTemperature ?? this.ambientTemperature,
      ambientHumidity: ambientHumidity ?? this.ambientHumidity,
      ambientPressure: ambientPressure ?? this.ambientPressure,
      inhalationCount: inhalationCount ?? this.inhalationCount,
      inhalationDuration: inhalationDuration ?? this.inhalationDuration,
      correctTechnique: correctTechnique ?? this.correctTechnique,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Helper method to parse DoseSyncStatus from string
  static DoseSyncStatus _parseDoseSyncStatus(String? status) {
    if (status == null) return DoseSyncStatus.unknown;
    
    switch (status.toLowerCase()) {
      case 'quick':
        return DoseSyncStatus.quick;
      case 'late':
        return DoseSyncStatus.late;
      case 'correct':
        return DoseSyncStatus.correct;
      default:
        return DoseSyncStatus.unknown;
    }
  }

  /// Helper method to parse DoseType from string
  static DoseType _parseDoseType(String? type) {
    if (type == null) return DoseType.unknown;
    
    switch (type.toLowerCase()) {
      case 'reliever':
        return DoseType.reliever;
      case 'controller':
        return DoseType.controller;
      case 'combination':
        return DoseType.combination;
      case 'other':
        return DoseType.other;
      default:
        return DoseType.unknown;
    }
  }

  /// Calculate predicted PEF based on patient demographics (example implementation)
  /// Note: This is a simplified example. Real calculations would be more complex.
  static double calculatePredictedPEF({
    required bool isMale,
    required int ageYears,
    required double heightCm,
  }) {
    if (isMale) {
      return (0.0614 * heightCm - 0.0292 * ageYears + 1.0523) * 60;
    } else {
      return (0.0550 * heightCm - 0.0301 * ageYears + 0.8710) * 60;
    }
  }

  /// Calculate PEF as percentage of predicted value
  double calculatePefPercentage({
    required bool isMale,
    required int ageYears,
    required double heightCm,
  }) {
    final predicted = calculatePredictedPEF(
      isMale: isMale,
      ageYears: ageYears,
      heightCm: heightCm,
    );
    
    return (pef / predicted) * 100;
  }

  /// Interpret PEF reading based on percentage of predicted value
  /// Returns a zone classification (green, yellow, red)
  String interpretPefZone(double pefPercentage) {
    if (pefPercentage >= 80) {
      return 'green'; // Good control
    } else if (pefPercentage >= 50) {
      return 'yellow'; // Caution - take action
    } else {
      return 'red'; // Medical alert - seek immediate help
    }
  }

  /// Check if the measurement indicates a potential asthma attack
  bool isPotentialAsthmaAttack() {
    // This is a simplified example. Real detection would use more complex algorithms.
    return pef < 50 || fev1 < 1.0;
  }

  /// Get a human-readable description of the dose synchronization status
  String getDoseSyncDescription() {
    switch (doseSyncStatus) {
      case DoseSyncStatus.quick:
        return 'Inhaled too quickly before medication release';
      case DoseSyncStatus.late:
        return 'Inhaled too late after medication release';
      case DoseSyncStatus.correct:
        return 'Perfect synchronization with medication release';
      case DoseSyncStatus.unknown:
        return 'Synchronization status unknown';
    }
  }

  /// Get a human-readable description of the dose type
  String getDoseTypeDescription() {
    switch (doseType) {
      case DoseType.reliever:
        return 'Quick-relief medication';
      case DoseType.controller:
        return 'Long-term control medication';
      case DoseType.combination:
        return 'Combination medication (controller + reliever)';
      case DoseType.other:
        return 'Other medication type';
      case DoseType.unknown:
        return 'Unknown medication type';
    }
  }

  @override
  String toString() {
    return 'DataMetrics(id: $id, timestamp: $timestamp, pef: $pef L/min, fev1: $fev1 L)';
  }
}