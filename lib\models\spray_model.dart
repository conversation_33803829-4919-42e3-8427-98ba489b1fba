/// Enum for spray names
enum SprayName {
  iprol,
  aerol,
  erva,
  cortis,
  raforex,
  cyvax;

  /// Get display name for the spray
  String get displayName {
    switch (this) {
      case SprayName.iprol:
        return 'Iprol';
      case SprayName.aerol:
        return 'Aerol';
      case SprayName.erva:
        return 'Erva';
      case SprayName.cortis:
        return 'Cortis';
      case SprayName.raforex:
        return 'Raforex';
      case SprayName.cyvax:
        return 'Cyvax';
    }
  }
}

/// Enum for number of doses
enum NumberOfDoses {
  doses100(100),
  doses120(120),
  doses200(200);

  const NumberOfDoses(this.value);
  final int value;

  /// Get display string
  String get displayName => '$value doses';
}

/// Enum for dose types (in mcg)
enum DoseType {
  mcg12(12),
  mcg20(20),
  mcg25(25),
  mcg100(100),
  mcg125(125),
  mcg250(250);

  const DoseType(this.value);
  final int value;

  /// Get display string
  String get displayName => '$value mcg';
}

/// Enum for spray colors
enum SprayColor {
  yellow,
  blue,
  red,
  jam,
  green,
  pink,
  purple;

  /// Get display name for the color
  String get displayName {
    switch (this) {
      case SprayColor.yellow:
        return 'Yellow';
      case SprayColor.blue:
        return 'Blue';
      case SprayColor.red:
        return 'Red';
      case SprayColor.jam:
        return 'Jam';
      case SprayColor.green:
        return 'Green';
      case SprayColor.pink:
        return 'Pink';
      case SprayColor.purple:
        return 'Purple';
    }
  }
}

/// Spray model class for storing and managing Medicament information
/// Designed for future Supabase integration
class SprayModel {
  /// Required fields
  final SprayName name;
  final NumberOfDoses numberOfDoses;
  final DoseType doseType;

  /// Optional fields with default values
  final SprayColor color;

  SprayModel({
    // Required fields
    required this.name,
    required this.numberOfDoses,
    required this.doseType,
    // Optional fields with default values
    this.color = SprayColor.blue,
  });

  /// Create a copy of this spray with some fields updated
  SprayModel copyWith({
    SprayName? name,
    NumberOfDoses? numberOfDoses,
    DoseType? doseType,
    SprayColor? color,
  }) {
    return SprayModel(
      name: name ?? this.name,
      numberOfDoses: numberOfDoses ?? this.numberOfDoses,
      doseType: doseType ?? this.doseType,
      color: color ?? this.color,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name.name,
      'numberOfDoses': numberOfDoses.value,
      'doseType': doseType.value,
      'color': color.name,
    };
  }

  /// Create from JSON
  factory SprayModel.fromJson(Map<String, dynamic> json) {
    return SprayModel(
      name: SprayName.values.firstWhere(
        (e) => e.name == json['name'],
        orElse: () => SprayName.aerol,
      ),
      numberOfDoses: NumberOfDoses.values.firstWhere(
        (e) => e.value == json['numberOfDoses'],
        orElse: () => NumberOfDoses.doses200,
      ),
      doseType: DoseType.values.firstWhere(
        (e) => e.value == json['doseType'],
        orElse: () => DoseType.mcg25,
      ),
      color: SprayColor.values.firstWhere(
        (e) => e.name == json['color'],
        orElse: () => SprayColor.blue,
      ),
    );
  }

  /// String representation
  @override
  String toString() {
    return 'SprayModel(name: ${name.displayName}, doses: ${numberOfDoses.displayName}, '
        'doseType: ${doseType.displayName}, color: ${color.displayName})';
  }

  /// Demo spray data for testing and development
  static List<SprayModel> demoSprays = [
    SprayModel(
      name: SprayName.erva,
      numberOfDoses: NumberOfDoses.doses120,
      color: SprayColor.red,
      doseType: DoseType.mcg250,
    ),
    SprayModel(
      name: SprayName.aerol,
      numberOfDoses: NumberOfDoses.doses200,
      color: SprayColor.blue,
      doseType: DoseType.mcg100,
    ),
    SprayModel(
      name: SprayName.iprol,
      numberOfDoses: NumberOfDoses.doses200,
      color: SprayColor.yellow,
      doseType: DoseType.mcg20,
    ),
    SprayModel(
      name: SprayName.cortis,
      numberOfDoses: NumberOfDoses.doses200,
      color: SprayColor.jam,
      doseType: DoseType.mcg250,
    ),
    SprayModel(
      name: SprayName.raforex,
      numberOfDoses: NumberOfDoses.doses100,
      color: SprayColor.green,
      doseType: DoseType.mcg12,
    ),
    SprayModel(
      name: SprayName.cyvax,
      numberOfDoses: NumberOfDoses.doses120,
      color: SprayColor.pink,
      doseType: DoseType.mcg125,
    ),
    SprayModel(
      name: SprayName.cyvax,
      numberOfDoses: NumberOfDoses.doses120,
      color: SprayColor.purple,
      doseType: DoseType.mcg250,
      
    ),
  ];
}
