import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';

class PatientHomePage extends StatefulWidget {
  const PatientHomePage({Key? key}) : super(key: key);

  @override
  State<PatientHomePage> createState() => _PatientHomePageState();
}

class _PatientHomePageState extends State<PatientHomePage> with RouteAware {
  final GlobalKey<State<HeaderWidget>> _headerKey =
      GlobalKey<State<HeaderWidget>>();

  @override
  void didPopNext() {
    // Called when returning to this page from another page
    // Refresh header to sync notification state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_headerKey.currentState != null) {
        (_headerKey.currentState as dynamic).refreshData?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                HeaderWidget(key: _headerKey),
                const SizedBox(height: 16),                const ActionButtonsWidget(),
                const SizedBox(height: 24),
                GestureDetector(
                  onTap: () {
                    // TODO : Navigate to summary page
                  },
                  child: const SummaryWidget(),
                ),
                const SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: const [
                          MedicationReminderWidget(),
                          SizedBox(height: 16),
                          TreatmentWidget(),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(maxHeight: 375),
                        child: const DeviceWidget(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const WeatherWidget(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
