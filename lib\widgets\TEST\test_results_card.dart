import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/models/test_results.dart';
import 'package:befine/services/pdf_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/common/loading_widget.dart';

class TestResultsCard extends StatefulWidget {
  final TestResults results;
  final MeasurementData measurementData;

  const TestResultsCard({
    super.key,
    required this.results,
    required this.measurementData,
  });

  @override
  State<TestResultsCard> createState() => _TestResultsCardState();
}

class _TestResultsCardState extends State<TestResultsCard> {
  final PdfService _pdfService = PdfService();
  bool _isGeneratingPdf = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.12),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.assessment,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultats du Test',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      Text(
                        'Analyse complète des données',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                children: [
                  _buildResultRow(
                    'DIP (Débit Inspiratoire de Pointe)',
                    '${(-widget.results.dip).toStringAsFixed(2)} L/s',
                    Icons.speed,
                    AppTheme.primaryColor,
                  ),
                  const Divider(height: 24),
                  _buildResultRow(
                    'Durée d\'Inspiration',
                    '${widget.results.inhalationDuration.toStringAsFixed(2)} s',
                    Icons.timer,
                    Colors.blue,
                  ),
                  const Divider(height: 24),
                  _buildResultRow(
                    'Temps du DIP',
                    '${widget.results.dipTime.toStringAsFixed(2)} s',
                    Icons.schedule,
                    Colors.orange,
                  ),
                  const Divider(height: 24),
                  _buildResultRow(
                    'Volume Maximum',
                    '${widget.results.maxVolume.toStringAsFixed(2)} L',
                    Icons.air,
                    Colors.green,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            if (_isGeneratingPdf)
              const LoadingWidget(message: 'Génération du PDF en cours...')
            else
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _shareResults(context),
                      icon: const Icon(Icons.share, size: 20),
                      label: const Text(
                        'Partager',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _printResults(context),
                      icon: const Icon(Icons.print, size: 20),
                      label: const Text(
                        'Imprimer',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(
                          color: AppTheme.primaryColor,
                          width: 2,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.12),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _shareResults(BuildContext context) async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfBytes = await _pdfService.generatePdf(
        measurementData: widget.measurementData,
        testResults: widget.results,
        context: context,
      );

      final filePath = await _pdfService.savePdf(pdfBytes);

      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });

        if (filePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('PDF partagé avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors du partage du PDF'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _printResults(BuildContext context) async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfBytes = await _pdfService.generatePdf(
        measurementData: widget.measurementData,
        testResults: widget.results,
        context: context,
      );

      await _pdfService.printPdf(pdfBytes);

      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'impression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
