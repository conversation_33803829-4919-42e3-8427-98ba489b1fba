# ESP32 Flow Sensor Setup

This project uses an ESP32 microcontroller with a Sensirion differential pressure sensor to measure flow and volume data, which is then transmitted to a Flutter app via Bluetooth.

## Required Libraries

1. **ESP32 Arduino Core** - Make sure you have the ESP32 board support installed in Arduino IDE
   - In Arduino IDE: Tools > Board > Boards Manager > Search for "ESP32" and install

2. **BluetoothSerial** - This is included with the ESP32 Arduino core

3. **ArduinoJson** - For formatting data as JSON
   - In Arduino IDE: Sketch > Include Library > Manage Libraries > Search for "ArduinoJson" and install

4. **Sensirion I2C SDP** - For interfacing with the differential pressure sensor
   - In Arduino IDE: Sketch > Include Library > Manage Libraries > Search for "Sensirion I2C SDP" and install

## Hardware Setup

1. Connect the Sensirion SDP sensor to the ESP32 using I2C:
   - SDP SDA → ESP32 GPIO 6
   - SDP SCL → ESP32 GPIO 7
   - VCC → 3.3V
   - GND → GND

## Uploading the Code

1. Open the SerialFlowBuffer.ino file in Arduino IDE
2. Select the correct ESP32 board from Tools > Board
3. Select the correct port from Tools > Port
4. Click Upload

## Pairing with Your Device

1. Power on the ESP32
2. On your mobile device, go to Bluetooth settings
3. Enable Bluetooth
4. Scan for devices
5. Select "ESP32_Flow_Sensor" from the list
6. Complete the pairing process

## Troubleshooting

If you encounter issues:

1. **Compilation errors**:
   - Make sure all required libraries are installed
   - Check that you have selected the correct board in Arduino IDE

2. **Connection issues**:
   - Ensure the ESP32 is powered on
   - Check that Bluetooth is enabled on your mobile device
   - Try restarting both the ESP32 and your mobile device

3. **No data received**:
   - Check the serial monitor for debugging information
   - Verify that the sensor is properly connected
   - Make sure the Flutter app is sending the "START_MEASUREMENT" command

## Data Format

The ESP32 sends data in JSON format:
```json
{
  "time": 1.25,    // Time in seconds since measurement started
  "flow": -0.75,   // Flow rate in liters/second
  "volume": 1.5    // Accumulated volume in liters
}
```
