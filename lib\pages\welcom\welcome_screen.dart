import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/models/role_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
//import 'package:befine/pages/sign/sign_in_screen.dart';
//import 'package:befine/pages/sign/sign_up_screen.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with SingleTickerProviderStateMixin {
  int _selectedRoleIndex = 0;
  final List<RoleModel> _roles = RoleModel.roles;

  // Animation controller for the fade-in effect
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
    _animationController.forward();
    init_();
  }

  void init_() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'role',
      _roles[_selectedRoleIndex].id,
    ); // Save role ID string
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onRoleSelected(int index) async {
    if (_selectedRoleIndex != index) {
      setState(() {
        _selectedRoleIndex = index;
      });
      _animationController.reset();
      _animationController.forward();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'role',
        _roles[_selectedRoleIndex].id,
      ); // Save role ID string
    }
  }

  Widget _buildRoleSelector() {
    return SizedBox(
      height: 120,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _roles.length,
          (index) => GestureDetector(
            onTap: () => _onRoleSelected(index),
            child: Container(
              width: 140,
              margin: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                color:
                    _selectedRoleIndex == index
                        ? AppTheme.primaryColor
                        : AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(_roles[index].iconPath, height: 50, width: 50),
                  const SizedBox(height: 12),
                  Text(
                    _roles[index].title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color:
                          _selectedRoleIndex == index
                              ? Colors.white
                              : AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoleDescription() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: Container(
        key: ValueKey<int>(_selectedRoleIndex),
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          _roles[_selectedRoleIndex].description,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 16, color: AppTheme.primaryColor),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24.0,
              vertical: 16.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Center(
                  child: Image.asset(
                    'assets/images/befine_logo.png',
                    height: 200,
                  ),
                ),
                // const SizedBox(height: 10),
                Text(
                  'Bienvenue sur BeFine',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  'Votre SmartInhalateur personnel pour la gestion de la BPCO et de l\'asthme',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Text(
                  'Sélectionnez votre rôle',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                _buildRoleSelector(),
                const SizedBox(height: 15),
                _buildRoleDescription(),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to sign in screen
                    Navigator.of(context).pushNamed('/sign-in');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'Se Connecter',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 16),
                OutlinedButton(
                  onPressed: () async {
                    // Navigate to the appropriate sign-up screen based on selected role
                    final route =
                        _selectedRoleIndex == 0
                            ? '/sign-up-patient'
                            : '/sign-up-doctor';
                    Navigator.of(context).pushNamed(route);
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    minimumSize: const Size(double.infinity, 50),
                    side: BorderSide(color: AppTheme.primaryColor, width: 2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Créer un Compte',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 24),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'En continuant, vous acceptez nos ',
                      style: TextStyle(color: AppTheme.textSecondaryColor),
                    ),
                    GestureDetector(
                      onTap: () {
                        // Show terms and conditions dialog
                        showDialog(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                title: const Text('Conditions Générales'),
                                content: const SingleChildScrollView(
                                  child: Text(
                                    'En utilisant BeFine, vous acceptez nos pratiques de collecte et de traitement des données à des fins de surveillance de la santé. '
                                    'Nous priorisons votre vie privée et sécurisons vos informations personnelles et de santé. '
                                    'L\'application fournit du contenu informatif uniquement et ne remplace pas les conseils médicaux professionnels. '
                                    'Vous êtes responsable du maintien de la sécurité de votre compte et de l\'exactitude des informations fournies. '
                                    'BeFine se réserve le droit de modifier ces conditions avec un préavis approprié.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('Fermer'),
                                  ),
                                ],
                              ),
                        );
                      },
                      child: const Text(
                        'Conditions Générales',
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
