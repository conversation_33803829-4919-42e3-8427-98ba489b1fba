import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:graph_app/services/ble_service.dart';
import 'package:provider/provider.dart';

class BleConnectionWidget extends StatefulWidget {
  const BleConnectionWidget({super.key});

  @override
  State<BleConnectionWidget> createState() => _BleConnectionWidgetState();
}

class _BleConnectionWidgetState extends State<BleConnectionWidget> {
  List<BluetoothDevice> _devices = [];
  bool _isScanning = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Check Bluetooth status on init
    _checkBluetoothStatus();
  }

  Future<void> _checkBluetoothStatus() async {
    try {
      if (await FlutterBluePlus.isSupported == false) {
        setState(() {
          _errorMessage = 'Bluetooth non pris en charge sur cet appareil';
        });
        return;
      }

      // Check if Bluetooth is on
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        setState(() {
          _errorMessage =
              'Veuillez activer le Bluetooth pour rechercher des appareils';
        });

        // Try to turn on Bluetooth on Android
        if (!Platform.isIOS && Platform.isAndroid) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            // User may have denied permission
            setState(() {
              _errorMessage =
                  'Veuillez activer le Bluetooth dans les paramètres';
            });
          }
        }
      } else {
        setState(() {
          _errorMessage = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation du Bluetooth: $e';
      });
    }
  }

  Future<void> _scanForDevices() async {
    // Check Bluetooth status before scanning
    await _checkBluetoothStatus();
    if (!mounted) return;

    if (_errorMessage != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Réparer',
            textColor: Colors.white,
            onPressed: () async {
              if (Platform.isAndroid) {
                try {
                  await FlutterBluePlus.turnOn();
                  await _checkBluetoothStatus();
                } catch (e) {
                  // Ignore errors
                }
              }
            },
          ),
        ),
      );
      return;
    }

    setState(() {
      _isScanning = true;
      _devices = [];
      _errorMessage = null;
    });

    // Show scanning toast
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recherche d\'appareils ESP32...'),
        backgroundColor: Colors.blue,
        duration: Duration(
          seconds: 1,
        ), // Shorter duration since scan is faster now
      ),
    );

    try {
      final bleService = Provider.of<BleService>(context, listen: false);
      final devices = await bleService.scanForDevices();

      if (!mounted) return;

      setState(() {
        _devices = devices;
        _isScanning = false;
      });

      if (devices.isEmpty) {
        setState(() {
          _errorMessage =
              'Aucun appareil ESP32 trouvé. Assurez-vous que votre appareil est allumé et à proximité.';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Aucun appareil ESP32 trouvé'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Réessayer',
              textColor: Colors.white,
              onPressed: _scanForDevices,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${devices.length} appareil(s) trouvé(s)'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isScanning = false;
        _errorMessage = 'Erreur de recherche: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur de recherche: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Réessayer',
            textColor: Colors.white,
            onPressed: _scanForDevices,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bleService = Provider.of<BleService>(context);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Connexion BLE',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                _buildStatusIndicator(bleService.connectionState),
              ],
            ),
            const SizedBox(height: 16),
            _buildConnectionContent(bleService),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BleConnectionState state) {
    Color color;
    String text;

    switch (state) {
      case BleConnectionState.connected:
        color = Colors.green;
        text = 'Connecté';
        break;
      case BleConnectionState.connecting:
        color = Colors.orange;
        text = 'Connexion en cours';
        break;
      case BleConnectionState.scanning:
        color = Colors.blue;
        text = 'Recherche';
        break;
      case BleConnectionState.error:
        color = Colors.red;
        text = 'Erreur';
        break;
      case BleConnectionState.disconnected:
        color = Colors.grey;
        text = 'Déconnecté';
        break;
    }

    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(text),
      ],
    );
  }

  Widget _buildConnectionContent(BleService bleService) {
    if (bleService.connectionState == BleConnectionState.connected) {
      return _buildConnectedContent(bleService);
    } else {
      return _buildScanContent(bleService);
    }
  }

  Widget _buildConnectedContent(BleService bleService) {
    // Get device name or ID
    final deviceName =
        bleService.connectedDevice?.platformName ?? 'Unknown Device';
    final deviceId = bleService.connectedDevice?.remoteId.toString() ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Connecté à: $deviceName',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        if (deviceId.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              'ID: $deviceId',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
        const SizedBox(height: 16),
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: () => bleService.disconnectDevice(),
              icon: const Icon(Icons.bluetooth_disabled),
              label: const Text('Déconnecter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScanContent(BleService bleService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Sélectionnez un appareil:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 0, 0, 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: const Color.fromRGBO(255, 0, 0, 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        const SizedBox(height: 8),
        SizedBox(
          height: 150,
          child:
              _isScanning
                  ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Recherche rapide du capteur de débit ESP32...'),
                      ],
                    ),
                  )
                  : _devices.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.bluetooth_searching,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage ??
                              'Aucun appareil trouvé. Appuyez sur "Rechercher" pour lancer la recherche.',
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    shrinkWrap: true,
                    itemCount: _devices.length,
                    itemBuilder: (context, index) {
                      final device = _devices[index];
                      final deviceName =
                          device.platformName.isNotEmpty
                              ? device.platformName
                              : device.advName.isNotEmpty
                              ? device.advName
                              : 'Unknown Device';

                      return ListTile(
                        leading: const Icon(
                          Icons.bluetooth,
                          color: Colors.blue,
                        ),
                        title: Text(deviceName),
                        subtitle: Text(device.remoteId.toString()),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () => _connectToDevice(device),
                      );
                    },
                  ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ElevatedButton.icon(
              onPressed: _isScanning ? null : _scanForDevices,
              icon: const Icon(Icons.search),
              label: Text(
                _isScanning ? 'Recherche rapide...' : 'Rechercher rapidement',
              ),
            ),
            if (Platform.isAndroid)
              TextButton.icon(
                onPressed: () async {
                  try {
                    await FlutterBluePlus.turnOn();
                    await _checkBluetoothStatus();
                    if (!mounted) return;
                  } catch (e) {
                    if (!mounted) return;
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('Error: $e')));
                  }
                },
                icon: const Icon(Icons.bluetooth),
                label: const Text('ON'),
              ),
          ],
        ),
      ],
    );
  }

  void _connectToDevice(BluetoothDevice device) async {
    setState(() {
      _errorMessage = null;
      _isScanning = false; // Stop scanning UI indicator
    });

    final bleService = Provider.of<BleService>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show connecting dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Connexion'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                "Connexion à ${device.platformName.isNotEmpty ? device.platformName : "l'appareil"}...",
              ),
              const SizedBox(height: 8),
              const Text(
                'Cela peut prendre quelques instants.',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      },
    );

    try {
      final success = await bleService.connectToDevice(device);

      // Close the dialog
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!mounted) return;

      if (!success) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text(
              'Échec de la connexion à l\'appareil. Veuillez réessayer.',
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Connecté avec succès!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Close the dialog
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Erreur de connexion: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Réessayer',
            textColor: Colors.white,
            onPressed: () => _connectToDevice(device),
          ),
        ),
      );
    }
  }
}
