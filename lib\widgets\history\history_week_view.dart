import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/history/history_day_card.dart';

class HistoryWeekView extends StatelessWidget {
  const HistoryWeekView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the current date
    final DateTime now = DateTime.now();

    // Generate a list of the last 7 days (including today)
    final List<DateTime> lastSevenDays = List.generate(
      7,
      (index) => now.subtract(Duration(days: index)),
    );

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: lastSevenDays.length,
      itemBuilder: (context, index) {
        final day = lastSevenDays[index];
        // Activity count (this would come from your data)
        final int activityCount =
            5 - index; // Just an example, replace with real data
        return HistoryDayCard(
          date: day,
          isToday: index == 0,
          activityCount: activityCount > 0 ? activityCount : 1,
          onTap: () {
            // Navigate to day detail view
            // You would implement this navigation
          },
        );
      },
    );
  }
}
