import 'package:flutter/material.dart';
import 'package:befine/models/chat_message.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:intl/intl.dart';

/// Widget for displaying individual chat messages
class ChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final bool showTimestamp;

  const ChatMessageWidget({
    Key? key,
    required this.message,
    this.showTimestamp = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isUser = message.sender == MessageSender.user;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Column(
        crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isUser) ...[
                _buildAvatar(isUser: false),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: _buildMessageBubble(context, isUser),
              ),
              if (isUser) ...[
                const SizedBox(width: 8),
                _buildAvatar(isUser: true),
              ],
            ],
          ),
          if (showTimestamp) ...[
            const SizedBox(height: 4),
            _buildTimestamp(context, isUser),
          ],
        ],
      ),
    );
  }

  /// Build avatar for user or assistant
  Widget _buildAvatar({required bool isUser}) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isUser ? AppTheme.primaryColor : AppTheme.surfaceColor,
        border: Border.all(
          color: isUser ? AppTheme.primaryColor.withOpacity(0.3) : AppTheme.borderColor,
          width: 1,
        ),
      ),
      child: Icon(
        isUser ? Icons.person : Icons.medical_services,
        size: 18,
        color: isUser ? Colors.white : AppTheme.primaryColor,
      ),
    );
  }

  /// Build message bubble
  Widget _buildMessageBubble(BuildContext context, bool isUser) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isUser ? AppTheme.primaryColor : AppTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: Radius.circular(isUser ? 20 : 4),
          bottomRight: Radius.circular(isUser ? 4 : 20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: message.isLoading ? _buildLoadingIndicator() : _buildMessageContent(isUser),
    );
  }

  /// Build message content
  Widget _buildMessageContent(bool isUser) {
    return Text(
      message.content,
      style: TextStyle(
        color: isUser ? Colors.white : AppTheme.textPrimaryColor,
        fontSize: 16,
        height: 1.4,
      ),
    );
  }

  /// Build loading indicator for assistant messages
  Widget _buildLoadingIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Typing...',
          style: TextStyle(
            color: AppTheme.textSecondaryColor,
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// Build timestamp
  Widget _buildTimestamp(BuildContext context, bool isUser) {
    final timeFormat = DateFormat('HH:mm');
    return Padding(
      padding: EdgeInsets.only(
        left: isUser ? 0 : 40,
        right: isUser ? 40 : 0,
      ),
      child: Text(
        timeFormat.format(message.timestamp),
        style: TextStyle(
          color: AppTheme.textSecondaryColor,
          fontSize: 12,
        ),
      ),
    );
  }
}
