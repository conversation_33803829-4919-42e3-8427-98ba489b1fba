import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenRouter API communication with GPT models
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '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';
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  static const String _model = 'openai/gpt-4o-mini';

  // Enhanced system prompt for the medical assistant
  static const String _systemPrompt =
      '''Vous êtes <PERSON>, un assistant médical IA professionnel et compatissant spécialisé dans l'accompagnement des patients atteints de maladies respiratoires chroniques (BPCO, asthme). Vous travaillez en collaboration avec le dispositif SmartInhealer pour optimiser les soins respiratoires.

🎯 VOTRE MISSION :
Éduquer, soutenir et guider les patients - JAMAIS diagnostiquer ou prescrire des traitements.

📋 PROTOCOLE DE COMMUNICATION :
• Répondez TOUJOURS en français
• Adoptez un ton empathique, professionnel et rassurant
• Utilisez un langage simple, clair et non alarmant
• Structurez vos réponses avec des puces ou numéros pour la clarté
• Limitez vos réponses à 200-300 mots maximum

🚨 PROTOCOLES D'URGENCE - ACTIONS IMMÉDIATES :
Si le patient mentionne :
• Difficultés respiratoires sévères ou détresse respiratoire
• Douleurs thoraciques intenses
• Lèvres ou ongles bleus (cyanose)
• Incapacité à parler en phrases complètes
• Utilisation excessive de l'inhalateur de secours (>4 fois/jour)
• Fièvre élevée avec difficultés respiratoires

RÉPONSE OBLIGATOIRE : "🚨 URGENCE : Ces symptômes nécessitent une attention médicale IMMÉDIATE. Contactez le 15 (SAMU) ou rendez-vous aux urgences sans délai. N'attendez pas."

🔧 DOMAINES D'EXPERTISE SPÉCIALISÉS :

1. TECHNIQUE D'INHALATION OPTIMALE :
• Techniques spécifiques pour inhalateurs-doseurs (Iprol, Aerol, Erva, Cortis, Raforex, Cyvax)
• Coordination inspiration-activation
• Vitesse d'inhalation appropriée (30-60 L/min)
• Temps de rétention (10 secondes minimum)
• Utilisation de chambres d'inhalation

2. INTERPRÉTATION DES DONNÉES SMARTINHEALER :
• Débit Inspiratoire de Pointe (DIP) : valeurs normales 30-60 L/min
• Durée d'inhalation optimale : 2-4 secondes
• Volume maximal inhalé
• Synchronisation dose-inhalation
• Analyse des courbes débit/volume

3. GESTION MÉDICAMENTEUSE :
• Adhérence thérapeutique sans modification de posologie
• Différenciation traitement de fond vs traitement de crise
• Effets secondaires courants et gestion
• Importance du rinçage buccal post-inhalation

4. SURVEILLANCE SYMPTÔMES RESPIRATOIRES :
• Échelle de dyspnée (0-4)
• Fréquence et caractéristiques de la toux
• Expectoration (couleur, consistance)
• Variations circadiennes des symptômes
• Facteurs déclenchants environnementaux

❌ LIMITES STRICTES - NE JAMAIS :
• Modifier, arrêter ou prescrire des médicaments
• Interpréter des examens médicaux (radiographies, analyses)
• Donner des conseils contradictoires aux prescriptions médicales
• Minimiser des symptômes préoccupants
• Remplacer une consultation médicale

✅ EXEMPLES DE RÉPONSES APPROPRIÉES :
"Selon vos données SmartInhealer, votre DIP de 45 L/min est dans la normale. Pour optimiser votre technique..."
"Vos symptômes suggèrent une possible exacerbation. Je recommande de contacter votre pneumologue..."

❌ EXEMPLES DE RÉPONSES INAPPROPRIÉES :
"Vous pouvez augmenter votre dose de..."
"Ce n'est pas grave, ne vous inquiétez pas..."
"Vous n'avez pas besoin de voir un médecin..."

🔄 STRUCTURE DE CONVERSATION :
1. Accueil empathique et écoute active
2. Questions de clarification si nécessaire
3. Conseils éducatifs personnalisés
4. Référence aux données SmartInhealer si pertinent
5. Encouragement à l'observance thérapeutique
6. Rappel de suivi médical approprié

💡 CONSEILS COMPLÉMENTAIRES :
• Techniques de respiration (respiration diaphragmatique, lèvres pincées)
• Modifications environnementales (éviction allergènes, qualité air)
• Activité physique adaptée
• Gestion du stress et relaxation
• Importance de la vaccination (grippe, pneumocoque)

🏥 ESCALADE MÉDICALE SYSTÉMATIQUE :
Terminez TOUJOURS par : "Ces conseils complètent votre suivi médical. Consultez votre pneumologue pour toute question spécifique à votre traitement. Continuez à utiliser votre SmartInhealer pour optimiser vos inhalations."

Votre objectif : Autonomiser le patient dans la gestion de sa maladie respiratoire tout en respectant strictement les limites de sécurité médicale.''';

  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenRouter service
  void _initializeOpenAI() {
    debugPrint(
      'Initializing OpenRouter with API key: ${_apiKey.substring(0, 20)}...',
    );
    debugPrint('OpenRouter initialized successfully');
  }

  /// Add welcome message from assistant in French
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "👋 Bonjour ! Je suis BeFine, votre assistant médical IA spécialisé en santé respiratoire.\n\n🔧 Je peux vous accompagner sur :\n• Techniques d'inhalation optimales\n• Interprétation de vos données SmartInhealer\n• Gestion de vos symptômes respiratoires\n• Conseils d'observance thérapeutique\n• Techniques de respiration et conseils lifestyle\n\n⚠️ Important : Je ne remplace jamais votre médecin. En cas d'urgence respiratoire, contactez le 15.\n\nComment puis-je vous aider aujourd'hui ? 😊",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from OpenRouter GPT
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      debugPrint('Sending message to OpenRouter GPT: $userMessage');

      // Prepare conversation history for the API
      final conversationMessages = _buildConversationHistory();

      // Call OpenRouter chat completion API
      final response = await _callChatCompletion(conversationMessages);

      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add assistant response
      final assistantMessage = ChatMessage.assistant(content: response);
      _messages.add(assistantMessage);
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      // Enhanced debugging
      debugPrint('=== DETAILED ERROR INFORMATION ===');
      debugPrint('Error: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error details: ${e.toString()}');
      debugPrint('=== END ERROR INFORMATION ===');

      // Provide more specific error messages based on the actual error
      String errorMessage;
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('401') || errorString.contains('unauthorized')) {
        errorMessage =
            "J'ai des difficultés avec ma configuration. Veuillez contacter le support.";
      } else if (errorString.contains('404') ||
          errorString.contains('not found')) {
        errorMessage =
            "J'ai des difficultés à accéder à ma configuration d'assistant. Veuillez contacter le support.";
      } else if (errorString.contains('429') ||
          errorString.contains('rate limit')) {
        errorMessage =
            "Je subis actuellement une forte demande. Veuillez réessayer dans quelques instants.";
      } else if (errorString.contains('500') ||
          errorString.contains('server')) {
        errorMessage =
            "Je rencontre des problèmes de serveur. Veuillez réessayer plus tard.";
      } else if (errorString.contains('network') ||
          errorString.contains('connection') ||
          errorString.contains('timeout')) {
        errorMessage =
            "J'ai des difficultés de connexion en ce moment. Veuillez vérifier votre connexion internet et réessayer.";
      } else {
        errorMessage =
            "Je rencontre un problème temporaire. Veuillez réessayer de poser votre question. (Debug: ${e.toString()})";
      }

      final assistantErrorMessage = ChatMessage.assistant(
        content: errorMessage,
      );
      _messages.add(assistantErrorMessage);
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Build conversation history for OpenRouter API
  List<Map<String, String>> _buildConversationHistory() {
    final conversationMessages = <Map<String, String>>[];

    // Add system prompt
    conversationMessages.add({'role': 'system', 'content': _systemPrompt});

    // Add conversation history (excluding loading messages)
    for (final message in _messages) {
      if (!message.isLoading) {
        conversationMessages.add({
          'role': message.sender == MessageSender.user ? 'user' : 'assistant',
          'content': message.content,
        });
      }
    }

    return conversationMessages;
  }

  /// Call OpenRouter chat completion API
  Future<String> _callChatCompletion(List<Map<String, String>> messages) async {
    debugPrint('Calling OpenRouter chat completion...');

    final response = await http.post(
      Uri.parse('$_baseUrl/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'HTTP-Referer':
            'https://befine.app', // Optional: for OpenRouter analytics
        'X-Title':
            'BeFine Medical Assistant', // Optional: for OpenRouter analytics
      },
      body: json.encode({
        'model': _model,
        'messages': messages,
        'max_tokens': 1000,
        'temperature': 0.7,
        'stream': false,
      }),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final content = responseData['choices'][0]['message']['content'];
      debugPrint(
        'OpenRouter response received: ${content.substring(0, 100)}...',
      );
      return content;
    } else {
      debugPrint(
        'OpenRouter API error: ${response.statusCode} - ${response.body}',
      );
      throw Exception(
        'Failed to get response from OpenRouter: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Test OpenRouter API connection
  Future<void> testAPI() async {
    debugPrint('=== TESTING OPENROUTER API CONNECTION ===');

    try {
      // Test: Check API key by making a simple chat completion request
      debugPrint('Testing OpenRouter API key with a simple request...');

      final testMessages = [
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': 'Hello, this is a test message.'},
      ];

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
          'HTTP-Referer': 'https://befine.app',
          'X-Title': 'BeFine Medical Assistant Test',
        },
        body: json.encode({
          'model': _model,
          'messages': testMessages,
          'max_tokens': 50,
          'temperature': 0.7,
        }),
      );

      debugPrint('OpenRouter API Status: ${response.statusCode}');
      debugPrint('OpenRouter API Response: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final content = responseData['choices'][0]['message']['content'];
        debugPrint('✅ OpenRouter API is working! Test response: $content');
      } else {
        debugPrint('❌ OpenRouter API key is invalid or has issues');
      }
    } catch (e) {
      debugPrint('❌ Test failed with error: $e');
    }

    debugPrint('=== END OPENROUTER API TEST ===');
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
