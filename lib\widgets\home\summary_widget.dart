import 'package:flutter/material.dart';
import 'circular_progress_widget.dart';

class SummaryWidget extends StatelessWidget {
  const SummaryWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your 30 days summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: const [
              CircularProgressWidget(
                value: 0.90,
                label: 'Schedule\nsuccess',
                color: Color(0xFF5BE584),
                icon: Icons.calendar_today,
                centerText: '90%',
              ),
              CircularProgressWidget(
                value: 0.97,
                label: 'Peak\nflow',
                color: Color(0xFF4695FF),
                icon: Icons.sync,
                centerText: '97%',
              ),
              CircularProgressWidget(
                value: 0.11,
                label: 'Emergency\nmeds',
                color: Color(0xFFFF68B9),
                icon: Icons.camera,
                centerText: '11',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

