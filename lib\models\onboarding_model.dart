class OnboardingModel {
  final String title;
  final String description;
  final String imagePath;

  OnboardingModel({
    required this.title,
    required this.description,
    required this.imagePath,
  });

  static List<OnboardingModel> onboardingContents = [
    OnboardingModel(
      title: 'Bienvenue sur BeFine',
      description:
          'Votre SmartInhalateur personnel pour gérer l\'asthme et la BPCO.',
      imagePath: 'assets/images/befine_logo.png',
    ),
    OnboardingModel(
      title: 'Surveillez Votre Santé',
      description:
          'Suivez vos métriques de santé respiratoire en temps réel avec notre appareil IoT.',
      imagePath: 'assets/images/multiple_devices.png',
    ),
    OnboardingModel(
      title: 'Logiciel de Surveillance Asthme & BPCO',
      description:
          'Des outils rapide et facile à utiliser pour les patients et les médecins.',
      imagePath: 'assets/images/software_toolkit.png',
    ),
  ];
}
