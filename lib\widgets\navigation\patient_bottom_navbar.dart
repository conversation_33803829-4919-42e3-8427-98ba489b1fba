import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:befine/theme/app_theme.dart';

class PatientBottomNavbar extends StatelessWidget {
  final PersistentTabController controller;
  final List<Widget> screens;

  const PatientBottomNavbar({
    super.key,
    required this.controller,
    required this.screens,
  });

  @override
  Widget build(BuildContext context) {
    return PersistentTabView(
      context,
      controller: controller,
      screens: screens,
      backgroundColor: Colors.white,
      handleAndroidBackButtonPress: true,
      resizeToAvoidBottomInset: true,
      stateManagement: true,
      hideNavigationBarWhenKeyboardAppears: true,
      decoration: NavBarDecoration(
        borderRadius: BorderRadius.circular(0),
        colorBehindNavBar: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      navBarStyle: NavBarStyle.style15,
      items: _buildNavBarItems(),
    );
  }

  List<PersistentBottomNavBarItem> _buildNavBarItems() {
    return [
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.home),
        title: "Accueil",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.medication_liquid_sharp),
        title: "Test",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.history),
        title: "Historique",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.calendar_today),
        title: "Rendez-vous",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.person),
        title: "Profil",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
    ];
  }
}
