import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:befine/widgets/profile/devices_card.dart';
import 'package:befine/widgets/profile/premium_features.dart';
import 'package:befine/widgets/profile/settings_panel.dart';
import 'package:befine/theme/app_theme.dart'; // Added import for app theme
import 'package:befine/widgets/profile/profile_header.dart'; // Added import for profile header widget
import 'package:befine/routes/app_routes.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/auth_service.dart';

class PatientProfilePage extends StatefulWidget {
  const PatientProfilePage({Key? key}) : super(key: key);

  @override
  _PatientProfilePageState createState() => _PatientProfilePageState();
}

class _PatientProfilePageState extends State<PatientProfilePage>
    with WidgetsBindingObserver, RouteAware {
  // User settings
  bool _notificationsEnabled = true;
  bool _remindersEnabled = true;
  bool _autoSyncEnabled = true;
  String _selectedLanguage = 'English';
  String _selectedTheme = 'system';
  String _userRole = 'patient';
  int _reminderTime = 10; // Added default reminder time (10 minutes)

  // Premium features
  final List<Map<String, dynamic>> _premiumFeatures = [
    {
      'icon': Icons.analytics_outlined,
      'title': 'Advanced Analytics',
      'description': 'Access detailed insights about your health patterns',
      'enabled': true,
    },
    {
      'icon': Icons.cloud_upload_outlined,
      'title': 'Cloud Backup',
      'description': 'Automatic backup of all your health data',
      'enabled': true,
    },
    {
      'icon': Icons.health_and_safety_outlined,
      'title': 'Health Reports',
      'description': 'Generate comprehensive weekly and monthly reports',
      'enabled': true,
    },
    {
      'icon': Icons.support_agent_outlined,
      'title': 'Priority Support',
      'description': '24/7 access to our support team',
      'enabled': true,
    },
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadUserPreferences();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      _loadUserPreferences();
    }
  }

  @override
  void didPopNext() {
    // Called when returning to this page from another page
    _loadUserPreferences();
  }

  @override
  void didPushNext() {
    // Called when navigating away from this page
  }

  @override
  void didPop() {
    // Called when this page is popped
  }

  @override
  void didPush() {
    // Called when this page is pushed
  }

  Future<void> _loadUserPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _userRole = prefs.getString('userRole') ?? 'patient';
      _selectedTheme = prefs.getString('theme') ?? 'system';
      _selectedLanguage = prefs.getString('language') ?? 'English';
      _notificationsEnabled = prefs.getBool('notifications') ?? true;
      _remindersEnabled = prefs.getBool('reminders') ?? true;
      _autoSyncEnabled = prefs.getBool('auto_connect_enabled') ?? true;
      _reminderTime =
          prefs.getInt('reminderTime') ??
          10; // Load reminder time with default 10 minutes
    });
  }

  // Added method to update reminder time
  Future<void> _updateReminderTime(int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('reminderTime', minutes);
    setState(() {
      _reminderTime = minutes;
    });
  }

  // Method to update notifications setting
  Future<void> _updateNotifications(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications', enabled);
    setState(() {
      _notificationsEnabled = enabled;
    });
  }

  // Method to update auto sync setting
  Future<void> _updateAutoSync(bool enabled) async {
    // Get service references before async operations
    final deviceService = Provider.of<DeviceService>(context, listen: false);
    final bleManager = Provider.of<BleManager>(context, listen: false);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('auto_connect_enabled', enabled);
    await deviceService.setAutoConnectEnabled(enabled);

    if (mounted) {
      setState(() {
        _autoSyncEnabled = enabled;
      });

      // Update BleManager auto-connect behavior
      if (enabled) {
        // Enable auto-connect: set device service and start timer
        bleManager.setDeviceService(deviceService);
      } else {
        // Disable auto-connect: stop the timer
        bleManager.restartAutoConnect();
      }
    }
  }

  Future<void> _loadThemeSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {});
  }

  Future<void> _updateThemeMode(String themeMode) async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _selectedTheme = themeMode;
    });
    // Notify the app to update the theme
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => PatientProfilePage()),
      );
    });
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Help & Support',
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppTheme.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Profile Settings',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Edit your profile information\n• Manage your connected devices\n• Configure app settings',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                const SizedBox(height: 16),
                Text(
                  'Premium Features',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Access advanced health analytics\n• Enable cloud backup\n• Generate detailed health reports',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,

      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ProfileHeader(userRole: _userRole),
            const SizedBox(height: 20),
            const DevicesCard(),
            const SizedBox(height: 20),
            SettingsPanel(
              notificationsEnabled: _notificationsEnabled,
              remindersEnabled: _remindersEnabled,
              autoSyncEnabled: _autoSyncEnabled,
              selectedLanguage: _selectedLanguage,
              selectedTheme: _selectedTheme,
              reminderTime: _reminderTime,
              onNotificationsChanged: _updateNotifications,
              onRemindersChanged:
                  (value) => setState(() => _remindersEnabled = value),
              onAutoSyncChanged: _updateAutoSync,
              onLanguageChanged:
                  (value) => setState(() => _selectedLanguage = value),
              onThemeChanged: _updateThemeMode,
              onReminderTimeChanged: _updateReminderTime,
            ),
            const SizedBox(height: 20),
            PremiumFeatures(features: _premiumFeatures),
            const SizedBox(height: 20),
            _buildLogoutButton(context),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ElevatedButton(
        onPressed: () async {
          // Get AuthService and Navigator before any async operations
          final authService = Provider.of<AuthService>(context, listen: false);
          final navigator = Navigator.of(context);

          final confirmed = await showDialog<bool>(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text(
                  'Logout',
                  style: TextStyle(
                    color: AppTheme.textPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: Text(
                  'Are you sure you want to logout?',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                backgroundColor: AppTheme.surfaceColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.textSecondaryColor,
                    ),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Logout'),
                  ),
                ],
              );
            },
          );

          if (confirmed == true) {
            // Sign out using AuthService
            await authService.signOut();

            // Navigate to welcome screen
            if (mounted) {
              navigator.pushNamedAndRemoveUntil(
                AppRoutes.welcome,
                (route) => false,
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          minimumSize: const Size(double.infinity, 50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: const Text(
          'Logout',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
