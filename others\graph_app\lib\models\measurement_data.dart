import 'package:graph_app/models/test_results.dart';

class MeasurementPoint {
  final double time; // Time in seconds
  final double value; // Flow rate or volume value

  MeasurementPoint({required this.time, required this.value});
}

class MeasurementData {
  final List<MeasurementPoint> flowData;
  final List<MeasurementPoint> volumeData;

  MeasurementData({required this.flowData, required this.volumeData});

  // Create an empty measurement data object
  factory MeasurementData.empty() {
    return MeasurementData(flowData: [], volumeData: []);
  }

  // Calculate test results
  TestResults calculateResults() {
    if (flowData.isEmpty || volumeData.isEmpty) {
      return TestResults.empty();
    }

    // Flow threshold for detecting inhalation (L/s)
    const double flowThreshold = -0.2;

    // Find DIP (minimum flow value, which is the most negative)
    double dip = 0.0;
    double dipTime = 0.0;

    // Find inhalation start and end times
    double inhalationStartTime = 0.0;
    double inhalationEndTime = 0.0;
    bool inhalationStarted = false;

    // Find maximum volume
    double maxVolume = 0.0;

    // Process flow data to find DIP and inhalation timing
    for (int i = 0; i < flowData.length; i++) {
      final point = flowData[i];

      // Check for DIP (most negative flow value)
      if (point.value < dip) {
        dip = point.value;
        dipTime = point.time;
      }

      // Detect inhalation start (when flow goes below threshold)
      if (!inhalationStarted && point.value < flowThreshold) {
        inhalationStarted = true;
        inhalationStartTime = point.time;
      }

      // Detect inhalation end (when flow goes above threshold after starting)
      if (inhalationStarted &&
          point.value > flowThreshold &&
          inhalationEndTime == 0.0) {
        inhalationEndTime = point.time;
      }
    }

    // If inhalation end wasn't detected, use the last time point
    if (inhalationEndTime == 0.0 && inhalationStarted && flowData.isNotEmpty) {
      inhalationEndTime = flowData.last.time;
    }

    // Calculate inhalation duration
    double inhalationDuration = inhalationEndTime - inhalationStartTime;

    // Find maximum volume
    for (final point in volumeData) {
      if (point.value > maxVolume) {
        maxVolume = point.value;
      }
    }

    // Return the calculated results
    return TestResults(
      dip: dip,
      dipTime: dipTime,
      inhalationDuration: inhalationDuration,
      maxVolume: maxVolume,
      inhalationStartTime: inhalationStartTime,
      inhalationEndTime: inhalationEndTime,
    );
  }

  // Add a new flow measurement point
  MeasurementData addFlowPoint(double time, double value) {
    final newFlowData = List<MeasurementPoint>.from(flowData)
      ..add(MeasurementPoint(time: time, value: value));

    return MeasurementData(flowData: newFlowData, volumeData: volumeData);
  }

  // Add a new volume measurement point
  MeasurementData addVolumePoint(double time, double value) {
    final newVolumeData = List<MeasurementPoint>.from(volumeData)
      ..add(MeasurementPoint(time: time, value: value));

    return MeasurementData(flowData: flowData, volumeData: newVolumeData);
  }

  // Clear all measurement data
  MeasurementData clear() {
    return MeasurementData.empty();
  }
}
