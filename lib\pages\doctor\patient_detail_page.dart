import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';

class PatientDetailPage extends StatefulWidget {
  final String patientId; // Add patient ID parameter

  const PatientDetailPage({Key? key, required this.patientId})
    : super(key: key);

  @override
  State<PatientDetailPage> createState() => _PatientDetailPageState();
}

class _PatientDetailPageState extends State<PatientDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Implement edit patient details
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Medical History'),
            Tab(text: 'Treatment Plan'),
            Tab(text: 'Device Readings'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildMedicalHistoryTab(),
          _buildTreatmentPlanTab(),
          _buildDeviceReadingsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'patient_detail_fab',
        onPressed: () {
          // Add new record/note/prescription
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPatientInfo(),
          const SizedBox(height: 16),
          _buildVitalStats(),
          const SizedBox(height: 16),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildPatientInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Personal Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Name', 'John Doe'),
            _buildInfoRow('Age', '45 years'),
            _buildInfoRow('Gender', 'Male'),
            _buildInfoRow('Blood Type', 'A+'),
            _buildInfoRow('Height', '175 cm'),
            _buildInfoRow('Weight', '80 kg'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildVitalStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Latest Vitals',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildVitalStatItem('Heart Rate', '72', 'bpm'),
                _buildVitalStatItem('Blood Pressure', '120/80', 'mmHg'),
                _buildVitalStatItem('SpO2', '98', '%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVitalStatItem(String label, String value, String unit) {
    return Column(
      children: [
        Text(label, style: const TextStyle(color: Colors.grey)),
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(unit),
      ],
    );
  }

  Widget _buildMedicalHistoryTab() {
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (context, index) {
        return _buildMedicalHistoryItem();
      },
    );
  }

  Widget _buildMedicalHistoryItem() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: const Text('COPD Diagnosis'),
        subtitle: const Text('Diagnosed on: 2023-01-15\nDr. Smith'),
        trailing: IconButton(
          icon: const Icon(Icons.chevron_right),
          onPressed: () {
            // View detailed medical record
          },
        ),
      ),
    );
  }

  Widget _buildTreatmentPlanTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) {
        return _buildTreatmentCard();
      },
    );
  }

  Widget _buildTreatmentCard() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Daily Inhaler Treatment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('Medication: Salbutamol 100mcg'),
            const Text('Frequency: 2 puffs, 4 times daily'),
            const Text('Duration: Ongoing'),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Edit treatment
                  },
                  child: const Text('Edit'),
                ),
                TextButton(
                  onPressed: () {
                    // Stop treatment
                  },
                  child: const Text('Stop'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceReadingsTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: DropdownButton<String>(
                  isExpanded: true,
                  value: 'Last 7 Days',
                  items:
                      ['Last 7 Days', 'Last 30 Days', 'Last 3 Months'].map((
                        String value,
                      ) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                  onChanged: (String? newValue) {
                    // Handle period change
                  },
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: 10,
            itemBuilder: (context, index) {
              return _buildDeviceReadingItem();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceReadingItem() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: const [
                Text(
                  'Peak Flow Reading',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text('2024-01-20 09:30 AM'),
              ],
            ),
            const SizedBox(height: 8),
            const Text('Value: 450 L/min'),
            const Text('Device: BeFine_01'),
            const LinearProgressIndicator(value: 0.75),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'Device Reading',
              'Peak Flow: 450 L/min',
              '2 hours ago',
              Icons.device_hub,
            ),
            _buildActivityItem(
              'Medication Taken',
              'Salbutamol 100mcg - 2 puffs',
              '4 hours ago',
              Icons.medication,
            ),
            _buildActivityItem(
              'Appointment',
              'Follow-up with Dr. Smith',
              'Yesterday',
              Icons.calendar_today,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String description,
    String time,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppTheme.primaryColor),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(description, style: const TextStyle(color: Colors.grey)),
              ],
            ),
          ),
          Text(time, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
