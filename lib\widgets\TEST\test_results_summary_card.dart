import 'package:flutter/material.dart';
import 'package:befine/models/test_results.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

/// Summary card for test results displayed at the top when test is complete
class TestResultsSummaryCard extends StatelessWidget {
  final TestResults results;
  final MeasurementData measurementData;
  final VoidCallback? onRestart;

  const TestResultsSummaryCard({
    super.key,
    required this.results,
    required this.measurementData,
    this.onRestart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with success icon and restart button
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.check_circle, color: Colors.green, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Terminé',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      'Résultats du test respiratoire',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              if (onRestart != null)
                IconButton(
                  onPressed: onRestart,
                  icon: Icon(Icons.refresh, color: AppTheme.primaryColor),
                  tooltip: 'Redémarrer le test',
                ),
            ],
          ),
          const SizedBox(height: 20),

          // Key results in a compact grid
          Row(
            children: [
              Expanded(
                child: _buildResultItem(
                  'DIP',
                  '${results.dip.toStringAsFixed(1)} L/min',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildResultItem(
                  'Volume Max',
                  '${results.maxVolume.toStringAsFixed(2)} L',
                  Icons.air,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildResultItem(
                  'Durée',
                  '${results.inhalationDuration.toStringAsFixed(1)}s',
                  Icons.timer,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Additional metrics
          Row(
            children: [
              Expanded(
                child: _buildResultItem(
                  'Temps DIP',
                  '${results.dipTime.toStringAsFixed(1)}s',
                  Icons.schedule,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildResultItem(
                  'Début',
                  '${results.inhalationStartTime.toStringAsFixed(1)}s',
                  Icons.play_arrow,
                  Colors.grey,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildResultItem(
                  'Fin',
                  '${results.inhalationEndTime.toStringAsFixed(1)}s',
                  Icons.stop,
                  Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
