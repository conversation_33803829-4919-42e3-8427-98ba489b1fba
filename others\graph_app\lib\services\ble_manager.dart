import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

enum BleConnectionState { disconnected, scanning, connecting, connected, error }

class BleManager extends ChangeNotifier {
  // Connection state
  BleConnectionState _connectionState = BleConnectionState.disconnected;
  BleConnectionState get connectionState => _connectionState;

  // Connected device
  BluetoothDevice? _connectedDevice;
  BluetoothDevice? get connectedDevice => _connectedDevice;

  // Discovered devices
  List<BluetoothDevice> _discoveredDevices = [];
  List<BluetoothDevice> get discoveredDevices => _discoveredDevices;

  // Stream subscriptions
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;

  // Constructor
  BleManager() {
    // Check if platform is supported
    if (Platform.isAndroid || Platform.isIOS) {
      // Set log level for debugging
      FlutterBluePlus.setLogLevel(LogLevel.verbose, color: true);

      // Initialize BLE
      _initBle();

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        debugPrint('Bluetooth adapter state: $state');
        if (state == BluetoothAdapterState.off) {
          // Bluetooth turned off, update state
          _connectionState = BleConnectionState.disconnected;
          notifyListeners();
        }
      });
    } else {
      // Platform not supported
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Initialize BLE
  Future<void> _initBle() async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      return;
    }

    try {
      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception("Bluetooth not supported on this device");
      }

      // Turn on Bluetooth if needed (Android only)
      if (Platform.isAndroid) {
        if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.off) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            debugPrint('Failed to turn on Bluetooth: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Bluetooth initialization error: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Start scanning for devices
  Future<void> startScan({Duration? timeout}) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return;
    }

    if (_connectionState == BleConnectionState.scanning) {
      return;
    }

    try {
      // Clear previous results
      _discoveredDevices = [];
      
      // Wait for Bluetooth to be ready
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        await FlutterBluePlus.adapterState
            .where((val) => val == BluetoothAdapterState.on)
            .first
            .timeout(
              const Duration(seconds: 5),
              onTimeout: () {
                throw Exception("Bluetooth not turned on in time");
              },
            );
      }

      _connectionState = BleConnectionState.scanning;
      notifyListeners();

      // Cancel any existing subscription
      await _scanResultsSubscription?.cancel();

      // Listen for scan results
      _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          for (ScanResult result in results) {
            if (!_discoveredDevices.contains(result.device)) {
              _discoveredDevices.add(result.device);
              notifyListeners();
            }
          }
        },
        onError: (e) {
          debugPrint('Error during scan: $e');
          _connectionState = BleConnectionState.error;
          notifyListeners();
        },
      );

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: timeout ?? const Duration(seconds: 10),
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((val) => val == false).first;

      // Update state
      if (_connectionState == BleConnectionState.scanning) {
        _connectionState = BleConnectionState.disconnected;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    } finally {
      // Make sure scanning is stopped
      if (FlutterBluePlus.isScanningNow) {
        await FlutterBluePlus.stopScan();
      }
    }
  }

  // Stop scanning
  Future<void> stopScan() async {
    if (FlutterBluePlus.isScanningNow) {
      await FlutterBluePlus.stopScan();
    }
    
    if (_connectionState == BleConnectionState.scanning) {
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDevice device) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return false;
    }

    if (_connectedDevice != null) {
      await disconnectDevice();
    }

    try {
      _connectionState = BleConnectionState.connecting;
      notifyListeners();

      // Set up connection state listener
      _connectionStateSubscription = device.connectionState.listen((state) {
        debugPrint('Device connection state changed: $state');
        if (state == BluetoothConnectionState.disconnected) {
          // Handle unexpected disconnection
          if (_connectionState == BleConnectionState.connected) {
            debugPrint('Device disconnected unexpectedly');
            _handleDisconnection();
          }
        }
      });

      // Connect to the device
      await device.connect(
        autoConnect: false,
        timeout: const Duration(seconds: 15),
      );

      _connectedDevice = device;
      _connectionState = BleConnectionState.connected;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      await disconnectDevice();
      return false;
    }
  }

  // Handle unexpected disconnection
  void _handleDisconnection() {
    _connectionState = BleConnectionState.disconnected;
    _connectedDevice = null;
    notifyListeners();
  }

  // Disconnect from device
  Future<void> disconnectDevice() async {
    try {
      // Cancel connection state subscription
      await _connectionStateSubscription?.cancel();
      _connectionStateSubscription = null;

      // Disconnect device if connected
      if (_connectedDevice != null) {
        try {
          await _connectedDevice!.disconnect();
        } catch (e) {
          debugPrint('Error during disconnect: $e');
          // Continue with cleanup even if disconnect fails
        }
      }

      // Clear reference
      _connectedDevice = null;

      // Update state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    } catch (e) {
      debugPrint('Error during disconnection cleanup: $e');
      // Ensure we still update the state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Get device battery level if available
  Future<int?> getDeviceBatteryLevel() async {
    if (_connectedDevice == null || _connectionState != BleConnectionState.connected) {
      return null;
    }

    try {
      // Discover services
      List<BluetoothService> services = await _connectedDevice!.discoverServices();
      
      // Look for battery service
      for (BluetoothService service in services) {
        if (service.uuid == Guid('0000180f-0000-1000-8000-00805f9b34fb')) { // Battery service UUID
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid == Guid('00002a19-0000-1000-8000-00805f9b34fb')) { // Battery level characteristic UUID
              final value = await c.read();
              return value.isNotEmpty ? value[0] : null;
            }
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting battery level: $e');
      return null;
    }
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _scanResultsSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _adapterStateSubscription?.cancel();

    // Disconnect device if connected
    if (_connectedDevice != null) {
      try {
        _connectedDevice!.disconnect();
      } catch (e) {
        debugPrint('Error disconnecting device during dispose: $e');
      }
    }

    debugPrint('BleManager disposed');
    super.dispose();
  }
}
