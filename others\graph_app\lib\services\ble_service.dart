import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:graph_app/models/measurement_data.dart';

enum BleConnectionState { disconnected, scanning, connecting, connected, error }

class BleService extends ChangeNotifier {
  // Connection state
  BleConnectionState _connectionState = BleConnectionState.disconnected;
  BleConnectionState get connectionState => _connectionState;

  // Connected device
  BluetoothDevice? _connectedDevice;
  BluetoothDevice? get connectedDevice => _connectedDevice;

  // BLE service and characteristic
  BluetoothService? _bleService;

  // Service and characteristic UUIDs (from ESP32 code)
  final Guid _serviceUuid = Guid("4fafc201-1fb5-459e-8fcc-c5c9c331914b");
  final Guid _dataCharacteristicUuid = Guid(
    "beb5483e-36e1-4688-b7f5-ea07361b26a8",
  );
  final Guid _statusCharacteristicUuid = Guid(
    "beb5483e-36e1-4688-b7f5-ea07361b26a9",
  );

  // Stream subscriptions
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;
  StreamSubscription<List<int>>? _dataSubscription;
  StreamSubscription<List<int>>? _statusSubscription;
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;

  // BLE characteristics
  BluetoothCharacteristic? _dataCharacteristic;
  BluetoothCharacteristic? _statusCharacteristic;

  // Measurement data
  MeasurementData _measurementData = MeasurementData.empty();
  MeasurementData get measurementData => _measurementData;

  // Test status
  String _testStatus = "disconnected";
  String get testStatus => _testStatus;

  // Test progress
  double _testProgress = 0.0;
  double get testProgress => _testProgress;

  // Maximum samples
  int _maxSamples = 128;
  int get maxSamples => _maxSamples;

  // Current sample count
  int _currentSample = 0;
  int get currentSample => _currentSample;

  // Stream controller for status updates
  final _statusStreamController = StreamController<String>.broadcast();
  Stream<String> get statusStream => _statusStreamController.stream;

  // Stream controller for measurement updates
  final _measurementStreamController =
      StreamController<MeasurementData>.broadcast();
  Stream<MeasurementData> get measurementStream =>
      _measurementStreamController.stream;

  // Constructor
  BleService() {
    // Check if platform is supported
    if (Platform.isAndroid || Platform.isIOS) {
      // Set log level for debugging
      FlutterBluePlus.setLogLevel(LogLevel.info, color: true);

      // Initialize BLE
      _initBle();

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        debugPrint('Bluetooth adapter state: $state');
        if (state == BluetoothAdapterState.off) {
          // Bluetooth turned off, update state
          _connectionState = BleConnectionState.disconnected;
          notifyListeners();
        }
      });
    } else {
      // Platform not supported
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      _testStatus = "unsupported";
      notifyListeners();
    }
  }

  // Initialize BLE
  Future<void> _initBle() async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      _testStatus = "unsupported";
      notifyListeners();
      return;
    }

    try {
      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception("Bluetooth not supported on this device");
      }

      // Turn on Bluetooth if needed (Android only)
      if (Platform.isAndroid) {
        if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.off) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            debugPrint('Failed to turn on Bluetooth: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Bluetooth initialization error: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Start scanning for devices
  Future<List<BluetoothDevice>> scanForDevices() async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return [];
    }

    if (_connectionState == BleConnectionState.scanning) {
      return [];
    }

    List<BluetoothDevice> discoveredDevices = [];
    StreamSubscription<List<ScanResult>>? scanSubscription;
    bool deviceFound = false;

    try {
      // Wait for Bluetooth to be ready
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        await FlutterBluePlus.adapterState
            .where((val) => val == BluetoothAdapterState.on)
            .first
            .timeout(
              const Duration(seconds: 5),
              onTimeout: () {
                throw Exception("Bluetooth not turned on in time");
              },
            );
      }

      _connectionState = BleConnectionState.scanning;
      notifyListeners();

      // Create a completer to resolve when a device is found or scan completes
      final completer = Completer<void>();

      // Create a subscription to scan results
      scanSubscription = FlutterBluePlus.onScanResults.listen(
        (results) {
          // Log all found devices for debugging
          for (ScanResult result in results) {
            debugPrint(
              'Found device: ${result.device.platformName} (${result.device.remoteId})',
            );
            debugPrint('  Adv name: ${result.advertisementData.advName}');
            debugPrint(
              '  Service UUIDs: ${result.advertisementData.serviceUuids}',
            );

            // Accept any device with "ESP32" in the name
            if ((result.device.platformName.contains("ESP32") ||
                    result.advertisementData.advName.contains("ESP32") ||
                    result.advertisementData.serviceUuids.contains(
                      _serviceUuid,
                    )) &&
                !discoveredDevices.contains(result.device)) {
              debugPrint('  Adding device to list');
              discoveredDevices.add(result.device);

              // Stop scanning as soon as we find an ESP32 device
              if (!deviceFound) {
                deviceFound = true;

                // Stop the scan
                FlutterBluePlus.stopScan().then((_) {
                  if (!completer.isCompleted) {
                    completer.complete();
                  }
                });
              }
            }
          }
        },
        onError: (e) {
          debugPrint('Error during scan: $e');
          if (!completer.isCompleted) {
            completer.completeError(e);
          }
        },
        onDone: () {
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
      );

      // Start scanning without service filter to find all devices
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 15), // This is a fallback timeout
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for either a device to be found or the scan to complete
      await completer.future;

      return discoveredDevices;
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      return [];
    } finally {
      // Cancel the subscription
      await scanSubscription?.cancel();

      // Make sure scanning is stopped
      if (FlutterBluePlus.isScanningNow) {
        await FlutterBluePlus.stopScan();
      }

      if (_connectionState == BleConnectionState.scanning) {
        _connectionState = BleConnectionState.disconnected;
        notifyListeners();
      }
    }
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDevice device) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return false;
    }

    if (_connectedDevice != null) {
      await disconnectDevice();
    }

    try {
      _connectionState = BleConnectionState.connecting;
      notifyListeners();

      // Set up connection state listener
      _connectionStateSubscription = device.connectionState.listen((state) {
        debugPrint('Device connection state changed: $state');
        if (state == BluetoothConnectionState.disconnected) {
          // Handle unexpected disconnection
          if (_connectionState == BleConnectionState.connected) {
            debugPrint('Device disconnected unexpectedly');
            _handleDisconnection();
          }
        }
      });

      // Register for cleanup when disconnected
      device.cancelWhenDisconnected(_connectionStateSubscription!, next: true);

      // Try to connect with retries
      int retryCount = 0;
      const maxRetries = 3;
      bool connected = false;

      while (!connected && retryCount < maxRetries) {
        try {
          debugPrint('Connecting to device, attempt ${retryCount + 1}');

          // Connect to the device with a timeout
          await device
              .connect(
                autoConnect: false,
                mtu: 512, // Request larger MTU for better data transfer
              )
              .timeout(
                const Duration(seconds: 10),
                onTimeout: () {
                  throw TimeoutException(
                    'Connection attempt ${retryCount + 1} timed out',
                  );
                },
              );

          connected = true;
          debugPrint('Connected successfully');
        } catch (e) {
          retryCount++;
          debugPrint('Connection attempt $retryCount failed: $e');

          if (retryCount >= maxRetries) {
            throw Exception('Failed to connect after $maxRetries attempts: $e');
          }

          // Wait before retrying
          await Future.delayed(const Duration(seconds: 1));
        }
      }

      _connectedDevice = device;

      // Discover services
      List<BluetoothService> services = await device.discoverServices();

      // Find our service
      BluetoothService? targetService;
      for (BluetoothService service in services) {
        if (service.uuid == _serviceUuid) {
          targetService = service;
          break;
        }
      }

      if (targetService == null) {
        debugPrint('Service not found: $_serviceUuid');
        await disconnectDevice();
        return false;
      }

      // Find our data characteristic
      BluetoothCharacteristic? dataCharacteristic;
      BluetoothCharacteristic? statusCharacteristic;

      for (BluetoothCharacteristic characteristic
          in targetService.characteristics) {
        if (characteristic.uuid == _dataCharacteristicUuid) {
          dataCharacteristic = characteristic;
        } else if (characteristic.uuid == _statusCharacteristicUuid) {
          statusCharacteristic = characteristic;
        }
      }

      if (dataCharacteristic == null) {
        debugPrint('Data characteristic not found: $_dataCharacteristicUuid');
        await disconnectDevice();
        return false;
      }

      if (statusCharacteristic == null) {
        debugPrint(
          'Status characteristic not found: $_statusCharacteristicUuid',
        );
        await disconnectDevice();
        return false;
      }

      _dataCharacteristic = dataCharacteristic;
      _statusCharacteristic = statusCharacteristic;

      // Enable notifications for data characteristic
      await _dataCharacteristic!.setNotifyValue(true);

      // Enable notifications for status characteristic
      await _statusCharacteristic!.setNotifyValue(true);

      // Subscribe to data notifications
      _dataSubscription = _dataCharacteristic!.onValueReceived.listen(
        _handleIncomingData,
        onError: (error) {
          debugPrint('Error receiving data: $error');
          disconnectDevice();
        },
      );

      // Subscribe to status notifications
      _statusSubscription = _statusCharacteristic!.onValueReceived.listen(
        _handleStatusUpdate,
        onError: (error) {
          debugPrint('Error receiving status: $error');
          disconnectDevice();
        },
      );

      // Register for cleanup when disconnected
      device.cancelWhenDisconnected(_dataSubscription!, next: true);
      device.cancelWhenDisconnected(_statusSubscription!, next: true);

      _connectionState = BleConnectionState.connected;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      await disconnectDevice();
      return false;
    }
  }

  // Handle unexpected disconnection
  void _handleDisconnection() {
    _connectionState = BleConnectionState.disconnected;
    _connectedDevice = null;
    _dataCharacteristic = null;
    notifyListeners();
  }

  // Handle status updates from the ESP32
  void _handleStatusUpdate(List<int> data) {
    if (data.isEmpty) return;

    try {
      // Convert bytes to string
      String dataString = utf8.decode(data);
      debugPrint('Received status: $dataString');

      // Parse JSON data
      Map<String, dynamic> jsonData = jsonDecode(dataString);

      // Extract status
      String status = jsonData['status'] ?? 'unknown';
      _testStatus = status;

      // Extract sample count if available
      if (jsonData.containsKey('samples')) {
        _currentSample = jsonData['samples'];
      }

      // Extract max samples if available
      if (jsonData.containsKey('maxSamples')) {
        _maxSamples = jsonData['maxSamples'];
      }

      // Extract progress if available
      if (jsonData.containsKey('progress')) {
        _testProgress = jsonData['progress'].toDouble();
      } else if (_maxSamples > 0 && _currentSample > 0) {
        // Calculate progress if not provided
        _testProgress = (_currentSample / _maxSamples) * 100.0;
      }

      // Notify listeners
      _statusStreamController.add(status);
      notifyListeners();
    } catch (e) {
      debugPrint('Error parsing status data: $e');
    }
  }

  // Disconnect from device
  Future<void> disconnectDevice() async {
    try {
      // Cancel all subscriptions
      if (_dataSubscription != null) {
        await _dataSubscription!.cancel();
        _dataSubscription = null;
      }

      if (_statusSubscription != null) {
        await _statusSubscription!.cancel();
        _statusSubscription = null;
      }

      if (_connectionStateSubscription != null) {
        await _connectionStateSubscription!.cancel();
        _connectionStateSubscription = null;
      }

      // Disconnect device if connected
      if (_connectedDevice != null) {
        try {
          await _connectedDevice!.disconnect();
        } catch (e) {
          debugPrint('Error during disconnect: $e');
          // Continue with cleanup even if disconnect fails
        }
      }

      // Clear all references
      _connectedDevice = null;
      _dataCharacteristic = null;
      _statusCharacteristic = null;

      // Reset status
      _testStatus = "disconnected";
      _testProgress = 0.0;
      _currentSample = 0;

      // Update state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    } catch (e) {
      debugPrint('Error during disconnection cleanup: $e');
      // Ensure we still update the state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Start a new measurement cycle
  Future<bool> startMeasurementCycle() async {
    if (_connectedDevice == null || _dataCharacteristic == null) {
      debugPrint('Cannot start measurement: device or characteristic is null');
      return false;
    }

    if (_connectionState != BleConnectionState.connected) {
      debugPrint('Cannot start measurement: device not connected');
      return false;
    }

    try {
      // Clear existing data
      _measurementData = _measurementData.clear();
      _measurementStreamController.add(_measurementData);

      // Reset test progress
      _testProgress = 0.0;
      _currentSample = 0;
      _testStatus = "measuring";
      notifyListeners();

      // Send command to ESP32 to start measurement
      await _sendCommand('START_MEASUREMENT');
      debugPrint('Measurement cycle started successfully');
      return true;
    } catch (e) {
      debugPrint('Error starting measurement: $e');
      return false;
    }
  }

  // Stop the current measurement cycle
  Future<bool> stopMeasurementCycle() async {
    if (_connectedDevice == null || _dataCharacteristic == null) {
      debugPrint('Cannot stop measurement: device or characteristic is null');
      return false;
    }

    if (_connectionState != BleConnectionState.connected) {
      debugPrint('Cannot stop measurement: device not connected');
      return false;
    }

    try {
      // Send command to ESP32 to stop measurement
      await _sendCommand('STOP_MEASUREMENT');
      _testStatus = "stopped";
      notifyListeners();
      debugPrint('Measurement cycle stopped successfully');
      return true;
    } catch (e) {
      debugPrint('Error stopping measurement: $e');
      return false;
    }
  }

  // Reset the measurement data
  Future<bool> resetMeasurementCycle() async {
    if (_connectedDevice == null || _dataCharacteristic == null) {
      debugPrint('Cannot reset measurement: device or characteristic is null');
      return false;
    }

    if (_connectionState != BleConnectionState.connected) {
      debugPrint('Cannot reset measurement: device not connected');
      return false;
    }

    try {
      // Clear existing data
      _measurementData = _measurementData.clear();
      _measurementStreamController.add(_measurementData);

      // Reset test progress
      _testProgress = 0.0;
      _currentSample = 0;
      _testStatus = "ready";
      notifyListeners();

      // Send command to ESP32 to reset measurement
      await _sendCommand('RESET_MEASUREMENT');
      debugPrint('Measurement cycle reset successfully');
      return true;
    } catch (e) {
      debugPrint('Error resetting measurement: $e');
      return false;
    }
  }

  // Send a command to the ESP32
  Future<void> _sendCommand(String command) async {
    if (_dataCharacteristic == null) {
      debugPrint('Cannot send command: characteristic is null');
      return;
    }

    try {
      // Write with response to ensure delivery
      await _dataCharacteristic!
          .write(utf8.encode(command), withoutResponse: false)
          .timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              throw TimeoutException('Command write timed out');
            },
          );

      debugPrint('Command sent: $command');
    } catch (e) {
      debugPrint('Error sending command: $e');
      rethrow; // Propagate error to caller
    }
  }

  // Handle incoming data from BLE
  void _handleIncomingData(List<int> data) {
    if (data.isEmpty) return;

    try {
      // Convert bytes to string
      String dataString = utf8.decode(data);
      debugPrint('Received data: $dataString');

      // Parse JSON data
      Map<String, dynamic> jsonData = jsonDecode(dataString);

      // Extract time, flow, and volume values
      double time = jsonData['time']?.toDouble() ?? 0.0;
      double flow = jsonData['flow']?.toDouble() ?? 0.0;
      double volume = jsonData['volume']?.toDouble() ?? 0.0;

      // Extract sample count and progress if available
      if (jsonData.containsKey('sample')) {
        _currentSample = jsonData['sample'];
      }

      if (jsonData.containsKey('progress')) {
        _testProgress = jsonData['progress'].toDouble();
      }

      // Update measurement data
      _measurementData = _measurementData
          .addFlowPoint(time, flow)
          .addVolumePoint(time, volume);

      // Notify listeners
      _measurementStreamController.add(_measurementData);
      notifyListeners();
    } catch (e) {
      debugPrint('Error parsing data: $e');
    }
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _dataSubscription?.cancel();
    _statusSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _adapterStateSubscription?.cancel();

    // Disconnect device if connected
    if (_connectedDevice != null) {
      try {
        _connectedDevice!.disconnect();
      } catch (e) {
        debugPrint('Error disconnecting device during dispose: $e');
      }
    }

    // Close stream controllers
    _measurementStreamController.close();
    _statusStreamController.close();

    debugPrint('BleService disposed');
    super.dispose();
  }
}
