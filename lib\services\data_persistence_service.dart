import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/models/patient_model.dart';
import 'package:befine/models/doctor_model.dart';

/// Service for managing data persistence and synchronization between
/// AuthService and SharedPreferences for optimal performance and offline capability
class DataPersistenceService {
  static final DataPersistenceService _instance = DataPersistenceService._internal();
  factory DataPersistenceService() => _instance;
  DataPersistenceService._internal();

  // SharedPreferences key constants
  static const String _patientPrefix = 'patient_';
  static const String _doctorPrefix = 'doctor_';
  static const String _userRoleKey = 'userRole';
  static const String _lastSyncKey = 'lastSync';

  /// Save complete patient profile to SharedPreferences
  Future<bool> savePatientProfile(PatientModel patient) async {
    try {
      debugPrint('Saving patient profile to SharedPreferences: ${patient.firstName} ${patient.lastName}');
      
      final prefs = await SharedPreferences.getInstance();
      
      // Save all patient fields with consistent naming
      await Future.wait([
        prefs.setString('${_patientPrefix}id', patient.id),
        prefs.setString('${_patientPrefix}firstName', patient.firstName),
        prefs.setString('${_patientPrefix}lastName', patient.lastName),
        prefs.setString('${_patientPrefix}email', patient.email),
        prefs.setString('${_patientPrefix}gender', patient.gender),
        prefs.setString('${_patientPrefix}dateOfBirth', patient.dateOfBirth),
        prefs.setDouble('${_patientPrefix}height', patient.height),
        prefs.setDouble('${_patientPrefix}weight', patient.weight),
        prefs.setInt('${_patientPrefix}phoneNumber', patient.phoneNumber),
        prefs.setString('${_patientPrefix}smokingStatus', patient.smokingStatus),
        prefs.setString('${_patientPrefix}bloodType', patient.bloodType),
        prefs.setString('${_patientPrefix}activityLevel', patient.activityLevel),
        prefs.setString('${_patientPrefix}notes', patient.notes),
        prefs.setString('${_patientPrefix}address', patient.address),
        prefs.setString(_userRoleKey, 'patient'),
        prefs.setString(_lastSyncKey, DateTime.now().toIso8601String()),
      ]);

      // Save optional fields
      if (patient.emergencyContact != null) {
        await prefs.setInt('${_patientPrefix}emergencyContact', patient.emergencyContact!);
      } else {
        await prefs.remove('${_patientPrefix}emergencyContact');
      }

      if (patient.profileImagePath != null) {
        await prefs.setString('${_patientPrefix}profileImagePath', patient.profileImagePath!);
      } else {
        await prefs.remove('${_patientPrefix}profileImagePath');
      }

      if (patient.createdAt != null) {
        await prefs.setString('${_patientPrefix}createdAt', patient.createdAt!.toIso8601String());
      }

      if (patient.updatedAt != null) {
        await prefs.setString('${_patientPrefix}updatedAt', patient.updatedAt!.toIso8601String());
      }

      debugPrint('Patient profile saved successfully to SharedPreferences');
      return true;
    } catch (e) {
      debugPrint('Error saving patient profile to SharedPreferences: $e');
      return false;
    }
  }

  /// Save complete doctor profile to SharedPreferences
  Future<bool> saveDoctorProfile(DoctorModel doctor) async {
    try {
      debugPrint('Saving doctor profile to SharedPreferences: ${doctor.firstName} ${doctor.lastName}');
      
      final prefs = await SharedPreferences.getInstance();
      
      // Save all doctor fields with consistent naming
      await Future.wait([
        prefs.setString('${_doctorPrefix}id', doctor.id),
        prefs.setString('${_doctorPrefix}firstName', doctor.firstName),
        prefs.setString('${_doctorPrefix}lastName', doctor.lastName),
        prefs.setString('${_doctorPrefix}email', doctor.email),
        prefs.setString('${_doctorPrefix}gender', doctor.gender),
        prefs.setInt('${_doctorPrefix}phoneNumber', doctor.phoneNumber),
        prefs.setString('${_doctorPrefix}specialty', doctor.specialty),
        prefs.setString('${_doctorPrefix}address', doctor.address),
        prefs.setString(_userRoleKey, 'doctor'),
        prefs.setString(_lastSyncKey, DateTime.now().toIso8601String()),
      ]);

      // Save optional fields
      if (doctor.licenseNumber != null) {
        await prefs.setInt('${_doctorPrefix}licenseNumber', doctor.licenseNumber!);
      } else {
        await prefs.remove('${_doctorPrefix}licenseNumber');
      }

      if (doctor.createdAt != null) {
        await prefs.setString('${_doctorPrefix}createdAt', doctor.createdAt!.toIso8601String());
      }

      if (doctor.updatedAt != null) {
        await prefs.setString('${_doctorPrefix}updatedAt', doctor.updatedAt!.toIso8601String());
      }

      debugPrint('Doctor profile saved successfully to SharedPreferences');
      return true;
    } catch (e) {
      debugPrint('Error saving doctor profile to SharedPreferences: $e');
      return false;
    }
  }

  /// Load patient profile from SharedPreferences
  Future<Map<String, dynamic>?> loadPatientProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if patient data exists
      if (!prefs.containsKey('${_patientPrefix}id')) {
        debugPrint('No patient profile found in SharedPreferences');
        return null;
      }

      final patientData = <String, dynamic>{
        'id': prefs.getString('${_patientPrefix}id'),
        'firstName': prefs.getString('${_patientPrefix}firstName') ?? '',
        'lastName': prefs.getString('${_patientPrefix}lastName') ?? '',
        'email': prefs.getString('${_patientPrefix}email') ?? '',
        'gender': prefs.getString('${_patientPrefix}gender') ?? 'Unknown',
        'dateOfBirth': prefs.getString('${_patientPrefix}dateOfBirth') ?? '1990-01-01',
        'height': prefs.getDouble('${_patientPrefix}height') ?? 0.0,
        'weight': prefs.getDouble('${_patientPrefix}weight') ?? 0.0,
        'phoneNumber': prefs.getInt('${_patientPrefix}phoneNumber') ?? 0,
        'smokingStatus': prefs.getString('${_patientPrefix}smokingStatus') ?? 'Unknown',
        'bloodType': prefs.getString('${_patientPrefix}bloodType') ?? 'Unknown',
        'activityLevel': prefs.getString('${_patientPrefix}activityLevel') ?? 'Unknown',
        'notes': prefs.getString('${_patientPrefix}notes') ?? 'Unknown',
        'address': prefs.getString('${_patientPrefix}address') ?? 'Unknown',
        'emergencyContact': prefs.getInt('${_patientPrefix}emergencyContact'),
        'profileImagePath': prefs.getString('${_patientPrefix}profileImagePath'),
        'createdAt': prefs.getString('${_patientPrefix}createdAt'),
        'updatedAt': prefs.getString('${_patientPrefix}updatedAt'),
      };

      debugPrint('Patient profile loaded from SharedPreferences: ${patientData['firstName']} ${patientData['lastName']}');
      return patientData;
    } catch (e) {
      debugPrint('Error loading patient profile from SharedPreferences: $e');
      return null;
    }
  }

  /// Load doctor profile from SharedPreferences
  Future<Map<String, dynamic>?> loadDoctorProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if doctor data exists
      if (!prefs.containsKey('${_doctorPrefix}id')) {
        debugPrint('No doctor profile found in SharedPreferences');
        return null;
      }

      final doctorData = <String, dynamic>{
        'id': prefs.getString('${_doctorPrefix}id'),
        'firstName': prefs.getString('${_doctorPrefix}firstName') ?? '',
        'lastName': prefs.getString('${_doctorPrefix}lastName') ?? '',
        'email': prefs.getString('${_doctorPrefix}email') ?? '',
        'gender': prefs.getString('${_doctorPrefix}gender') ?? 'Unknown',
        'phoneNumber': prefs.getInt('${_doctorPrefix}phoneNumber') ?? 0,
        'specialty': prefs.getString('${_doctorPrefix}specialty') ?? 'Unknown',
        'address': prefs.getString('${_doctorPrefix}address') ?? 'Unknown',
        'licenseNumber': prefs.getInt('${_doctorPrefix}licenseNumber'),
        'createdAt': prefs.getString('${_doctorPrefix}createdAt'),
        'updatedAt': prefs.getString('${_doctorPrefix}updatedAt'),
      };

      debugPrint('Doctor profile loaded from SharedPreferences: ${doctorData['firstName']} ${doctorData['lastName']}');
      return doctorData;
    } catch (e) {
      debugPrint('Error loading doctor profile from SharedPreferences: $e');
      return null;
    }
  }

  /// Get current user role from SharedPreferences
  Future<String?> getCurrentUserRole() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userRoleKey);
    } catch (e) {
      debugPrint('Error getting user role from SharedPreferences: $e');
      return null;
    }
  }

  /// Clear all user data from SharedPreferences
  Future<bool> clearUserData() async {
    try {
      debugPrint('Clearing all user data from SharedPreferences');
      final prefs = await SharedPreferences.getInstance();
      
      // Get all keys and remove user-related ones
      final keys = prefs.getKeys();
      final userKeys = keys.where((key) => 
        key.startsWith(_patientPrefix) || 
        key.startsWith(_doctorPrefix) ||
        key == _userRoleKey ||
        key == _lastSyncKey
      ).toList();

      for (final key in userKeys) {
        await prefs.remove(key);
      }

      debugPrint('User data cleared successfully from SharedPreferences');
      return true;
    } catch (e) {
      debugPrint('Error clearing user data from SharedPreferences: $e');
      return false;
    }
  }

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final syncTimeString = prefs.getString(_lastSyncKey);
      if (syncTimeString != null) {
        return DateTime.parse(syncTimeString);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting last sync time: $e');
      return null;
    }
  }

  /// Check if user data exists in SharedPreferences
  Future<bool> hasUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey('${_patientPrefix}id') || prefs.containsKey('${_doctorPrefix}id');
    } catch (e) {
      debugPrint('Error checking if user data exists: $e');
      return false;
    }
  }
}
