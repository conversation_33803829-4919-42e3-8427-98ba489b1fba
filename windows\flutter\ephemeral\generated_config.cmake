# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine" PROJECT_DIR)

set(FLUTTER_VERSION "0.1.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "PROJECT_DIR=D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine"
  "FLUTTER_TARGET=D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\PFE\\AndroidStudio\\Sami_V3\\befine\\BeFine\\.dart_tool\\package_config.json"
)
