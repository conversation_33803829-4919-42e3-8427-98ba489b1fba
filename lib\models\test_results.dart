/// Represents the calculated results from a breathing test
class TestResults {
  /// Peak Inspiratory Flow (Débit Inspiratoire de Pointe)
  final double dip;
  
  /// Inhalation duration in seconds
  final double inhalationDuration;
  
  /// Time at which DIP occurred (seconds from start)
  final double dipTime;
  
  /// Maximum volume reached during inhalation
  final double maxVolume;
  
  /// Inhalation start time
  final double inhalationStartTime;
  
  /// Inhalation end time
  final double inhalationEndTime;

  /// Constructor
  TestResults({
    required this.dip,
    required this.inhalationDuration,
    required this.dipTime,
    required this.maxVolume,
    required this.inhalationStartTime,
    required this.inhalationEndTime,
  });

  /// Create empty results
  factory TestResults.empty() {
    return TestResults(
      dip: 0.0,
      inhalationDuration: 0.0,
      dipTime: 0.0,
      maxVolume: 0.0,
      inhalationStartTime: 0.0,
      inhalationEndTime: 0.0,
    );
  }
}
