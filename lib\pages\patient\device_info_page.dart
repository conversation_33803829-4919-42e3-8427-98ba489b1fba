import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/models/spray_model.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/routes/app_routes.dart';

class DeviceInfoPage extends StatefulWidget {
  const DeviceInfoPage({super.key});

  @override
  State<DeviceInfoPage> createState() => _DeviceInfoPageState();
}

class _DeviceInfoPageState extends State<DeviceInfoPage> {
  SprayModel? _selectedSpray;
  bool _isEditingSpray = false;

  @override
  void initState() {
    super.initState();
    // Initialize with first demo spray as default
    _selectedSpray = SprayModel.demoSprays.first;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Device Information',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppTheme.textPrimaryColor),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed:
                () => Navigator.pushNamed(context, AppRoutes.manageDevices),
            tooltip: 'Manage Devices',
          ),
        ],
      ),
      body: Consumer2<DeviceService, BleManager>(
        builder: (context, deviceService, bleManager, child) {
          final deviceInfo = _getDeviceInfo(deviceService, bleManager);

          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Device Summary Card
                _buildDeviceSummaryCard(deviceInfo),
                const SizedBox(height: 20),

                // Spray Information Card
                _buildSprayInfoCard(),
                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Build device summary card
  Widget _buildDeviceSummaryCard(Map<String, dynamic> deviceInfo) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.devices_other,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      deviceInfo['name'],
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color:
                                deviceInfo['isConnected']
                                    ? Colors.green
                                    : Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          deviceInfo['isConnected']
                              ? 'Connected'
                              : 'Disconnected',
                          style: TextStyle(
                            color:
                                deviceInfo['isConnected']
                                    ? Colors.green
                                    : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Battery Level and Device Image Row (matching test card layout)
          Row(
            children: [
              // Battery Level and Device Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getBatteryIcon(deviceInfo['batteryLevel']),
                          color: _getBatteryColor(deviceInfo['batteryLevel']),
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Battery: ${deviceInfo['batteryLevel']}%',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Device Details
                    const Text(
                      'Device Details',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDeviceDetailRow(
                      'MAC Address',
                      _getMacAddress(deviceInfo),
                    ),
                    _buildDeviceDetailRow(
                      'Storage Status',
                      _getStorageStatus(deviceInfo),
                    ),
                    _buildDeviceDetailRow(
                      'Connection Type',
                      deviceInfo['isConnected'] ? 'BLE' : 'Offline',
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // Device Image
              Container(
                width: 100,
                height: 140,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Image.asset(
                    'assets/images/healer.png',
                    height: 80,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build spray information card
  Widget _buildSprayInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(Icons.medication, color: AppTheme.primaryColor, size: 24),
              const SizedBox(width: 2),
              const Text(
                'Current Spray',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 50),
              TextButton.icon(
                onPressed: () => _showSpraySelectionDialog(),
                icon: const Icon(Icons.edit, size: 18),
                label: const Text('Edit'),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_selectedSpray != null) ...[
            _buildSprayDetailRow('Name', _selectedSpray!.name.displayName),
            _buildSprayDetailRow(
              'Doses',
              _selectedSpray!.numberOfDoses.displayName,
            ),
            _buildSprayDetailRow(
              'Dose Type',
              _selectedSpray!.doseType.displayName,
            ),
            _buildSprayDetailRow('Color', _selectedSpray!.color.displayName),
          ] else ...[
            const Text(
              'No spray selected',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build spray detail row
  Widget _buildSprayDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build device details card
  Widget _buildDeviceDetailsCard(Map<String, dynamic> deviceInfo) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Device Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),

          _buildDetailRow('Device Type', 'SmartInhealer'),
          _buildDetailRow(
            'Connection',
            deviceInfo['isConnected'] ? 'BLE' : 'Offline',
          ),
          _buildDetailRow(
            'Status',
            deviceInfo['isConnected'] ? 'Active' : 'Inactive',
          ),
          _buildDetailRow('Last Sync', 'Just now'),

          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Row(
              children: [
                Icon(Icons.info_outline, color: AppTheme.primaryColor),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Device is ready for testing and monitoring.',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Show spray selection dialog
  void _showSpraySelectionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Spray'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: SprayModel.demoSprays.length,
              itemBuilder: (context, index) {
                final spray = SprayModel.demoSprays[index];
                final isSelected = _selectedSpray?.name == spray.name;

                return ListTile(
                  leading: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: _getSprayColor(spray.color),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  title: Text(spray.name.displayName),
                  subtitle: Text(
                    '${spray.doseType.displayName} • ${spray.numberOfDoses.displayName}',
                  ),
                  trailing:
                      isSelected
                          ? const Icon(
                            Icons.check,
                            color: AppTheme.primaryColor,
                          )
                          : null,
                  onTap: () {
                    setState(() {
                      _selectedSpray = spray;
                    });
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Get device information from services
  Map<String, dynamic> _getDeviceInfo(
    DeviceService deviceService,
    BleManager bleManager,
  ) {
    // Check if there's a connected device via BLE
    if (bleManager.connectionState == BleConnectionState.connected &&
        bleManager.connectedDevice != null) {
      final device = bleManager.connectedDevice!;
      final deviceId = device.remoteId.toString();

      // Get saved device info
      final savedDevice = deviceService.getDeviceById(deviceId);

      return {
        'name':
            savedDevice?.customName ??
            (device.platformName.isNotEmpty
                ? device.platformName
                : 'SmartInhealer'),
        'isConnected': true,
        'batteryLevel': savedDevice?.batteryLevel ?? 95,
      };
    }

    // Check if there are any saved devices
    final devices = deviceService.devices;
    if (devices.isNotEmpty) {
      final device =
          deviceService.lastConnectedDeviceId != null
              ? deviceService.getDeviceById(
                deviceService.lastConnectedDeviceId!,
              )
              : devices.first;

      if (device != null) {
        return {
          'name': device.customName ?? device.deviceType ?? 'SmartInhealer',
          'isConnected': device.isConnected ?? false,
          'batteryLevel': device.batteryLevel ?? 0,
        };
      }
    }

    // Default device info
    return {'name': 'No Device', 'isConnected': false, 'batteryLevel': 0};
  }

  /// Get battery icon based on level
  IconData _getBatteryIcon(int batteryLevel) {
    if (batteryLevel >= 90) return Icons.battery_full;
    if (batteryLevel >= 60) return Icons.battery_5_bar;
    if (batteryLevel >= 40) return Icons.battery_3_bar;
    if (batteryLevel >= 20) return Icons.battery_2_bar;
    if (batteryLevel > 0) return Icons.battery_1_bar;
    return Icons.battery_0_bar;
  }

  /// Get battery color based on level
  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel >= 50) return Colors.green;
    if (batteryLevel >= 20) return Colors.orange;
    return Colors.red;
  }

  /// Get actual color from spray color enum
  Color _getSprayColor(SprayColor sprayColor) {
    switch (sprayColor) {
      case SprayColor.yellow:
        return Colors.yellow;
      case SprayColor.blue:
        return Colors.blue;
      case SprayColor.red:
        return Colors.red;
      case SprayColor.jam:
        return Colors.purple.shade300;
      case SprayColor.green:
        return Colors.green;
      case SprayColor.pink:
        return Colors.pink;
      case SprayColor.purple:
        return Colors.purple;
    }
  }

  /// Build device detail row (for device summary card)
  Widget _buildDeviceDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Get MAC address from device info
  String _getMacAddress(Map<String, dynamic> deviceInfo) {
    // In a real implementation, this would come from the device service
    // For now, return a demo MAC address
    return 'AA:BB:CC:DD:EE:FF';
  }

  /// Get storage status from device info
  String _getStorageStatus(Map<String, dynamic> deviceInfo) {
    // In a real implementation, this would come from the device service
    // For now, return a demo storage status
    return 'OK';
  }
}
