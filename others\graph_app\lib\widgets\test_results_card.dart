import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:graph_app/models/measurement_data.dart';
import 'package:graph_app/models/test_results.dart';
import 'package:graph_app/services/pdf_service.dart';

class TestResultsCard extends StatefulWidget {
  final TestResults results;
  final MeasurementData measurementData;

  const TestResultsCard({
    super.key,
    required this.results,
    required this.measurementData,
  });

  @override
  State<TestResultsCard> createState() => _TestResultsCardState();
}

class _TestResultsCardState extends State<TestResultsCard> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue, size: 28),
                SizedBox(width: 8),
                Text(
                  'Résultats du Test',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const Divider(height: 24),
            // DIP (Débit Inspiratoire de Pointe)
            _buildResultRow(
              label: 'DIP (Débit Inspiratoire de Pointe)',
              value: '${(-widget.results.dip).toStringAsFixed(2)} L/s',
              isHighlighted: true,
              icon: Icons.trending_down,
              iconColor: const Color.fromARGB(255, 54, 206, 244),
            ),
            const SizedBox(height: 8),
            // Inhalation Duration
            _buildResultRow(
              label: 'Durée d\'Inspiration',
              value:
                  '${widget.results.inhalationDuration.toStringAsFixed(2)} s',
              isHighlighted: true,
              icon: Icons.timer,
              iconColor: Colors.green,
            ),
            const SizedBox(height: 8),
            // Inhalation Start/End
            _buildResultRow(
              label: 'Début/Fin d\'Inspiration',
              value:
                  '${widget.results.inhalationStartTime.toStringAsFixed(2)} s - ${widget.results.inhalationEndTime.toStringAsFixed(2)} s',
              icon: Icons.compare_arrows,
              iconColor: const Color.fromARGB(255, 33, 135, 243),
            ),
            const SizedBox(height: 8),
            // DIP Time
            _buildResultRow(
              label: 'Temps du DIP',
              value: '${widget.results.dipTime.toStringAsFixed(2)} s',
              icon: Icons.access_time,
              iconColor: Colors.orange,
            ),

            const SizedBox(height: 8),

            // Maximum Volume
            _buildResultRow(
              label: 'Volume Maximum',
              value: '${widget.results.maxVolume.toStringAsFixed(2)} L',
              icon: Icons.speed,
              iconColor: const Color.fromARGB(255, 0, 0, 0),
            ),

            const SizedBox(height: 16),

            // PDF Generation Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _savePdf(context),
                  icon:
                      Platform.isAndroid
                          ? const Icon(Icons.share)
                          : const Icon(Icons.save_alt),
                  label: Text(
                    Platform.isAndroid ? 'Partager PDF' : 'Enregistrer PDF',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _printPdf(context),
                  icon: const Icon(Icons.print),
                  label: const Text('Imprimer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Generate and save PDF
  Future<void> _savePdf(BuildContext initialContext) async {
    // Create PDF service
    final pdfService = PdfService();
    Uint8List? pdfBytes;

    try {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show loading indicator using the initial context
      _showLoadingDialog(
        initialContext,
        Platform.isAndroid
            ? 'Préparation du PDF pour partage...'
            : 'Génération du PDF en cours...',
      );

      // Generate PDF
      pdfBytes = await pdfService.generatePdf(
        measurementData: widget.measurementData,
        testResults: widget.results,
        context: initialContext,
      );

      // Close loading dialog if still mounted
      if (mounted) {
        Navigator.of(initialContext).pop();
      } else {
        return; // Exit if widget is no longer mounted
      }

      // Save PDF
      final filePath = await pdfService.savePdf(pdfBytes);

      // Handle result if still mounted
      if (!mounted) return;

      if (filePath != null) {
        // Show success message
        String message;
        if (Platform.isAndroid) {
          message = 'Rapport PDF partagé avec succès';
        } else {
          message = 'PDF enregistré avec succès à: $filePath';
        }

        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {
                if (mounted) {
                  ScaffoldMessenger.of(initialContext).hideCurrentSnackBar();
                }
              },
            ),
          ),
        );
      } else {
        // User cancelled the share dialog or permission was denied
        String message =
            Platform.isAndroid
                ? 'Partage du PDF annulé ou permissions refusées'
                : 'Enregistrement annulé';

        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.orange),
        );
      }
    } catch (e) {
      // Handle errors if still mounted
      if (mounted) {
        // Try to close any open dialogs
        try {
          if (Navigator.of(initialContext).canPop()) {
            Navigator.of(initialContext).pop();
          }
        } catch (_) {
          // Ignore errors when closing dialogs
        }

        // Show error message
        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la génération du PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Generate and print PDF
  Future<void> _printPdf(BuildContext initialContext) async {
    // Create PDF service
    final pdfService = PdfService();
    Uint8List? pdfBytes;

    try {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show loading indicator using the initial context
      _showLoadingDialog(initialContext, 'Préparation de l\'impression...');

      // Generate PDF
      pdfBytes = await pdfService.generatePdf(
        measurementData: widget.measurementData,
        testResults: widget.results,
        context: initialContext,
      );

      // Close loading dialog if still mounted
      if (mounted) {
        Navigator.of(initialContext).pop();
      } else {
        return; // Exit if widget is no longer mounted
      }

      // Print PDF
      await pdfService.printPdf(pdfBytes);
    } catch (e) {
      // Handle errors if still mounted
      if (mounted) {
        // Try to close any open dialogs
        try {
          if (Navigator.of(initialContext).canPop()) {
            Navigator.of(initialContext).pop();
          }
        } catch (_) {
          // Ignore errors when closing dialogs
        }

        // Show error message
        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'impression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show loading dialog
  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Expanded(child: Text(message)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildResultRow({
    required String label,
    required String value,
    bool isHighlighted = false,
    required IconData icon,
    required Color iconColor,
  }) {
    return Row(
      children: [
        Icon(icon, color: iconColor),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: isHighlighted ? 16 : 14,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlighted ? 16 : 14,
            fontWeight: FontWeight.bold,
            color: isHighlighted ? iconColor : null,
          ),
        ),
      ],
    );
  }
}
