-- Medical History table creation
CREATE TABLE medical_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES patients(id),
    condition_name text NOT NULL,
    diagnosis_date TIMESTAMP WITH TIME ZONE,
    status text, -- 'active', 'resolved', etc.
    notes TEXT
);

-- Enable RLS for medical_history table
--ALTER TABLE medical_history ENABLE ROW LEVEL SECURITY;

-- Treatment Plans table creation
CREATE TABLE treatment_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES patients(id),
    doctor_id UUID REFERENCES doctors(id),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    status text, -- 'active', 'completed', etc.
    description TEXT
);

-- Enable RLS for treatment_plans table
--ALTER TABLE treatment_plans ENABLE ROW LEVEL SECURITY;

-- Medications table creation
CREATE TABLE medications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    treatment_plan_id UUID REFERENCES treatment_plans(id),
    name text NOT NULL,
    dosage text,
    frequency text,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    notes TEXT
);

-- Enable RLS for medications table
--ALTER TABLE medications ENABLE ROW LEVEL SECURITY;

-- Create indexes for foreign keys
CREATE INDEX idx_patient_id ON medical_history(patient_id);
CREATE INDEX idx_treatment_plan_id ON medications(treatment_plan_id);