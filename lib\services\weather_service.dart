import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/models/weather_data.dart';

/// Service for fetching weather data from OpenWeather API
class WeatherService extends ChangeNotifier {
  static const String _apiKey = '4da32750991c422d43477f542f799978';
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';
  static const String _cacheKey = 'cached_weather_data';

  WeatherData? _currentWeather;
  bool _isLoading = false;
  String? _error;
  LocationData? _currentLocation;
  bool _isOffline = false;
  bool _isUsingCachedData = false;

  // Getters
  WeatherData? get currentWeather => _currentWeather;
  bool get isLoading => _isLoading;
  String? get error => _error;
  LocationData? get currentLocation => _currentLocation;
  bool get isOffline => _isOffline;
  bool get isUsingCachedData => _isUsingCachedData;

  /// Get current weather data for user's location
  Future<void> getCurrentWeather() async {
    try {
      _setLoading(true);
      _clearError();
      _isUsingCachedData = false;

      // Check internet connectivity
      final hasInternet = await _checkInternetConnection();
      _isOffline = !hasInternet;

      // Get user's current location
      final location = await _getCurrentLocation();
      if (location == null) {
        // Try to load cached data if location fails
        await _loadCachedWeatherData();
        if (_currentWeather == null) {
          throw Exception(
            'Unable to get current location and no cached data available',
          );
        }
        return;
      }

      _currentLocation = location;

      if (hasInternet) {
        try {
          // Fetch fresh weather data
          final weather = await _fetchWeatherByCoordinates(
            location.latitude,
            location.longitude,
          );

          _currentWeather = weather;
          _isUsingCachedData = false;

          // Cache the fresh data
          await _cacheWeatherData(weather);

          debugPrint(
            'Weather data fetched successfully for ${weather.cityName}',
          );
        } catch (e) {
          debugPrint('Failed to fetch fresh weather data: $e');
          // Fall back to cached data
          await _loadCachedWeatherData();
          if (_currentWeather == null) {
            throw Exception(
              'Failed to fetch weather data and no cached data available',
            );
          }
        }
      } else {
        // No internet, load cached data
        await _loadCachedWeatherData();
        if (_currentWeather == null) {
          throw Exception(
            'No internet connection and no cached weather data available',
          );
        }
      }
    } catch (e) {
      _setError('Failed to fetch weather data: ${e.toString()}');
      debugPrint('Error fetching weather: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get weather data by city name
  Future<void> getWeatherByCity(String cityName) async {
    try {
      _setLoading(true);
      _clearError();

      final weather = await _fetchWeatherByCity(cityName);
      _currentWeather = weather;
      debugPrint('Weather data fetched successfully for $cityName');
    } catch (e) {
      _setError('Failed to fetch weather data: ${e.toString()}');
      debugPrint('Error fetching weather for $cityName: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh current weather data
  Future<void> refreshWeather() async {
    if (_currentLocation != null) {
      await getCurrentWeather();
    } else {
      await getCurrentWeather();
    }
  }

  /// Get current location
  Future<LocationData?> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      debugPrint('Error getting location: $e');
      return null;
    }
  }

  /// Fetch weather data by coordinates
  Future<WeatherData> _fetchWeatherByCoordinates(
    double latitude,
    double longitude,
  ) async {
    final url = Uri.parse(
      '$_baseUrl/weather?lat=$latitude&lon=$longitude&appid=$_apiKey&units=metric',
    );

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return WeatherData.fromJson(data);
    } else {
      throw Exception('Failed to load weather data: ${response.statusCode}');
    }
  }

  /// Fetch weather data by city name
  Future<WeatherData> _fetchWeatherByCity(String cityName) async {
    final url = Uri.parse(
      '$_baseUrl/weather?q=$cityName&appid=$_apiKey&units=metric',
    );

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return WeatherData.fromJson(data);
    } else {
      throw Exception('Failed to load weather data: ${response.statusCode}');
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Clear all data
  void clearData() {
    _currentWeather = null;
    _currentLocation = null;
    _error = null;
    _isLoading = false;
    _isOffline = false;
    _isUsingCachedData = false;
    notifyListeners();
  }

  /// Check internet connectivity by making a simple HTTP request
  Future<bool> _checkInternetConnection() async {
    try {
      // Try to make a simple HTTP request to check connectivity
      final response = await http
          .get(
            Uri.parse('https://www.google.com'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Internet connectivity check failed: $e');
      return false;
    }
  }

  /// Cache weather data to SharedPreferences
  Future<void> _cacheWeatherData(WeatherData weather) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final weatherJson = json.encode(weather.toJson());
      await prefs.setString(_cacheKey, weatherJson);
      debugPrint('Weather data cached successfully');
    } catch (e) {
      debugPrint('Error caching weather data: $e');
    }
  }

  /// Load cached weather data from SharedPreferences
  Future<void> _loadCachedWeatherData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final weatherJson = prefs.getString(_cacheKey);

      if (weatherJson != null) {
        final weatherMap = json.decode(weatherJson) as Map<String, dynamic>;
        _currentWeather = WeatherData.fromCachedJson(weatherMap);
        _isUsingCachedData = true;
        debugPrint(
          'Loaded cached weather data for ${_currentWeather?.cityName}',
        );
        debugPrint('Cached data age: ${_currentWeather?.formattedLastUpdated}');
      } else {
        debugPrint('No cached weather data found');
      }
    } catch (e) {
      debugPrint('Error loading cached weather data: $e');
    }
  }
}
