import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/models/patient_model.dart';

class PatientProfilePage extends StatefulWidget {
  const PatientProfilePage({super.key});

  @override
  State<PatientProfilePage> createState() => _PatientProfilePageState();
}

class _PatientProfilePageState extends State<PatientProfilePage> {
  final _formKey = GlobalKey<FormState>();
  bool _isEditing = false;
  bool _isLoading = false;

  // Use a demo patient for now - in a real app, this would come from a database or state management
  late PatientModel _patient;

  // Controllers for text fields
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _telController = TextEditingController();
  final _birthdayController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _sosTelController = TextEditingController();
  final _notesController = TextEditingController();
  final _emailController = TextEditingController();

  // Dropdown values
  String? _selectedGender;
  String? _selectedSmokingStatus;
  String? _selectedActivityLevel;
  String? _selectedBloodType;

  @override
  void initState() {
    super.initState();
    // Initialize with demo data - in a real app, this would be the logged-in user's data
    _patient = PatientModel(
      id: 'demo-patient-id',
      firstName: 'John',
      lastName: 'Doe',
      gender: 'male',
      dateOfBirth: '1985-03-15',
      height: 175.0,
      weight: 80.0,
      phoneNumber: 12345678,
      email: '<EMAIL>',
      bloodType: 'A+',
      activityLevel: 'Moderate',
      address: '123 Main St, New York, NY 10001',
    );
    _loadPatientData();
  }

  void _loadPatientData() {
    // Populate text controllers with patient data
    _firstNameController.text = _patient.firstName;
    _lastNameController.text = _patient.lastName;
    _telController.text = _patient.phoneNumber.toString();
    _birthdayController.text = _patient.dateOfBirth;
    _heightController.text = _patient.height.toString();
    _weightController.text = _patient.weight.toString();
    _sosTelController.text = _patient.emergencyContact?.toString() ?? '';
    _notesController.text = _patient.notes;
    _emailController.text = _patient.email;

    // Set dropdown values
    _selectedGender = _patient.gender;
    _selectedSmokingStatus =
        _patient.smokingStatus.isNotEmpty
            ? _patient.smokingStatus
            : 'Never smoked';
    _selectedActivityLevel =
        _patient.activityLevel.isNotEmpty
            ? _patient.activityLevel
            : 'Sedentary';
    _selectedBloodType =
        _patient.bloodType.isNotEmpty ? _patient.bloodType : 'Unknown';
  }

  @override
  void dispose() {
    // Dispose controllers
    _firstNameController.dispose();
    _lastNameController.dispose();
    _telController.dispose();
    _birthdayController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _sosTelController.dispose();
    _notesController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);

      // In a real app, you would update the patient data in a database or state management
      // For now, we'll just simulate a delay and update our local model
      await Future.delayed(const Duration(seconds: 1));

      // Update the patient model with new values
      // In a real app, you would create a new instance or use a proper state management solution
      setState(() {
        _isLoading = false;
        _isEditing = false;
        // Show a success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Profile' : 'My Profile',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit, color: AppTheme.primaryColor),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.close, color: AppTheme.errorColor),
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _loadPatientData(); // Reset to original data
                });
              },
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: _isEditing ? _buildEditForm() : _buildProfileView(),
              ),
      floatingActionButton:
          _isEditing
              ? FloatingActionButton.extended(
                heroTag: 'edit_profile_fab',
                onPressed: _saveProfile,
                icon: const Icon(Icons.save),
                label: const Text('Save'),
                backgroundColor: AppTheme.primaryColor,
              )
              : null,
    );
  }

  Widget _buildProfileView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile Image and Basic Info
        Center(
          child: Column(
            children: [
              const CircleAvatar(
                radius: 60,
                backgroundColor: AppTheme.primaryColor,
                child: Icon(Icons.person, size: 80, color: Colors.white),
              ),
              const SizedBox(height: 16),
              Text(
                '${_patient.firstName} ${_patient.lastName}',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _patient.email,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Personal Information Section
        _buildSectionHeader('Personal Information'),
        _buildInfoCard([
          _buildInfoRow('Phone', _patient.phoneNumber.toString()),
          _buildInfoRow(
            'Gender',
            _patient.gender == 'male' ? 'Male' : 'Female',
          ),
          _buildInfoRow('Birthday', _patient.dateOfBirth),
          _buildInfoRow(
            'Emergency Contact',
            _patient.emergencyContact?.toString() ?? 'Not provided',
          ),
        ]),

        const SizedBox(height: 24),

        // Medical Information Section
        _buildSectionHeader('Medical Information'),
        _buildInfoCard([
          _buildInfoRow('Height', '${_patient.height} cm'),
          _buildInfoRow('Weight', '${_patient.weight} kg'),
          _buildInfoRow('Blood Type', _patient.bloodType),
          _buildInfoRow('Smoking Status', _patient.smokingStatus),
          _buildInfoRow('Activity Level', _patient.activityLevel),
        ]),

        const SizedBox(height: 24),

        // Notes Section
        if (_patient.notes.isNotEmpty) ...[
          _buildSectionHeader('Notes'),
          _buildInfoCard([
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(_patient.notes),
            ),
          ]),
          const SizedBox(height: 24),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(children: children),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppTheme.textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Image
          Center(
            child: Column(
              children: [
                Stack(
                  children: [
                    const CircleAvatar(
                      radius: 60,
                      backgroundColor: AppTheme.primaryColor,
                      child: Icon(Icons.person, size: 80, color: Colors.white),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            // TODO: Implement image picker
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Image picker not implemented yet',
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),

          // Personal Information Section
          _buildSectionHeader('Personal Information'),
          const SizedBox(height: 16),

          // First Name
          TextFormField(
            controller: _firstNameController,
            decoration: const InputDecoration(
              labelText: 'First Name',
              prefixIcon: Icon(Icons.person_outline),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your first name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Last Name
          TextFormField(
            controller: _lastNameController,
            decoration: const InputDecoration(
              labelText: 'Last Name',
              prefixIcon: Icon(Icons.person_outline),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your last name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Phone Number
          TextFormField(
            controller: _telController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              labelText: 'Phone Number',
              prefixIcon: Icon(Icons.phone),
            ),
          ),
          const SizedBox(height: 16),

          // Gender
          DropdownButtonFormField<String>(
            value: _selectedGender,
            decoration: const InputDecoration(
              labelText: 'Gender',
              prefixIcon: Icon(Icons.person_outline),
            ),
            items: const [
              DropdownMenuItem(value: 'male', child: Text('Male')),
              DropdownMenuItem(value: 'female', child: Text('Female')),
              DropdownMenuItem(value: 'other', child: Text('Other')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedGender = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select your Gender';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Birthday
          TextFormField(
            controller: _birthdayController,
            decoration: InputDecoration(
              labelText: 'Birthday',
              prefixIcon: const Icon(Icons.cake),
              suffixIcon: IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: () async {
                  // TODO: Implement date picker
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Date picker not implemented yet'),
                    ),
                  );
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your birthday';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Emergency Contact
          TextFormField(
            controller: _sosTelController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              labelText: 'SOS Phone Number',
              prefixIcon: Icon(Icons.emergency),
            ),
          ),
          const SizedBox(height: 32),

          // Medical Information Section
          _buildSectionHeader('Medical Information'),
          const SizedBox(height: 16),

          // Height
          TextFormField(
            controller: _heightController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Height (cm)',
              prefixIcon: Icon(Icons.height),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your height';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Weight
          TextFormField(
            controller: _weightController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Weight (kg)',
              prefixIcon: Icon(Icons.monitor_weight),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your weight';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Smoking Status
          DropdownButtonFormField<String>(
            value: _selectedSmokingStatus,
            decoration: const InputDecoration(
              labelText: 'Smoking Status',
              prefixIcon: Icon(Icons.smoking_rooms),
            ),
            items: const [
              DropdownMenuItem(
                value: 'Never smoked',
                child: Text('Never smoked'),
              ),
              DropdownMenuItem(
                value: 'Former smoker',
                child: Text('Former smoker'),
              ),
              DropdownMenuItem(
                value: 'Current Smoker',
                child: Text('Current Smoker'),
              ),
              DropdownMenuItem(
                value: 'Passive Smoker',
                child: Text('Passive Smoker'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _selectedSmokingStatus = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select your smoking status';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Activity Level
          DropdownButtonFormField<String>(
            value: _selectedActivityLevel,
            decoration: const InputDecoration(
              labelText: 'Activity Level',
              prefixIcon: Icon(Icons.directions_run),
            ),
            items: const [
              DropdownMenuItem(value: 'Sedentary', child: Text('Sedentary')),
              DropdownMenuItem(
                value: 'Lightly Active',
                child: Text('Lightly Active'),
              ),
              DropdownMenuItem(
                value: 'Moderately Active',
                child: Text('Moderately Active'),
              ),
              DropdownMenuItem(
                value: 'Very Active',
                child: Text('Very Active'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _selectedActivityLevel = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select your activity level';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Blood Type
          DropdownButtonFormField<String>(
            value: _selectedBloodType,
            decoration: const InputDecoration(
              labelText: 'Blood Type',
              prefixIcon: Icon(Icons.bloodtype),
            ),
            items: const [
              DropdownMenuItem(value: 'Unknown', child: Text('Unknown')),
              DropdownMenuItem(value: 'A+', child: Text('A+')),
              DropdownMenuItem(value: 'A-', child: Text('A-')),
              DropdownMenuItem(value: 'B+', child: Text('B+')),
              DropdownMenuItem(value: 'B-', child: Text('B-')),
              DropdownMenuItem(value: 'AB+', child: Text('AB+')),
              DropdownMenuItem(value: 'AB-', child: Text('AB-')),
              DropdownMenuItem(value: 'O+', child: Text('O+')),
              DropdownMenuItem(value: 'O-', child: Text('O-')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedBloodType = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select your blood type';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Notes
          TextFormField(
            controller: _notesController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Notes (Allergies, Medical Conditions, etc.)',
              prefixIcon: Icon(Icons.note),
            ),
          ),
          const SizedBox(height: 32),

          // Account Information Section
          _buildSectionHeader('Account Information'),
          const SizedBox(height: 16),

          // Email
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: 'Email',
              prefixIcon: Icon(Icons.email_outlined),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
