import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/services/auth_service.dart';
import 'package:befine/services/data_persistence_service.dart';
import 'package:befine/pages/patient/patient_profile_page.dart';

class HeaderWidget extends StatefulWidget {
  const HeaderWidget({Key? key}) : super(key: key);

  @override
  State<HeaderWidget> createState() => _HeaderWidgetState();
}

class _HeaderWidgetState extends State<HeaderWidget>
    with WidgetsBindingObserver {
  Map<String, String> _userData = {};
  File? _profileImage;
  bool _notificationsEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadUserData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      _loadUserData();
    }
  }

  Future<void> refreshData() async {
    await _loadUserData();
  }

  Future<void> _loadUserData() async {
    final persistenceService = DataPersistenceService();
    final authService = Provider.of<AuthService>(context, listen: false);

    if (!mounted) return;

    try {
      // Primary: Load from SharedPreferences for performance
      final patientData = await persistenceService.loadPatientProfile();

      setState(() {
        if (patientData != null) {
          // Use SharedPreferences data as primary source
          _userData = {
            'firstName': patientData['firstName'] ?? 'Patient',
            'lastName': patientData['lastName'] ?? '',
          };

          // Load profile image if exists
          String? imagePath = patientData['profileImagePath'];
          if (imagePath != null && imagePath.isNotEmpty) {
            _profileImage = File(imagePath);
          }
        } else if (authService.currentPatient != null) {
          // Fallback: Use AuthService data if SharedPreferences is empty
          _userData = {
            'firstName': authService.currentPatient!.firstName,
            'lastName': authService.currentPatient!.lastName,
          };

          // Save to SharedPreferences for next time
          persistenceService.savePatientProfile(authService.currentPatient!);
        } else {
          // Last resort: Use defaults
          _userData = {'firstName': 'Patient', 'lastName': ''};
        }

        // Load notification setting from SharedPreferences
        _loadNotificationSetting();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        setState(() {
          _userData = {'firstName': 'Patient', 'lastName': ''};
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadNotificationSetting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('notifications') ?? true;
    } catch (e) {
      debugPrint('Error loading notification setting: $e');
      _notificationsEnabled = true;
    }
  }

  Future<void> _toggleNotifications() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _notificationsEnabled = !_notificationsEnabled;
    });

    await prefs.setBool('notifications', _notificationsEnabled);

    // Show feedback to user
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _notificationsEnabled
                ? 'Notifications enabled'
                : 'Notifications disabled',
          ),
          backgroundColor: _notificationsEnabled ? Colors.green : Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        // Sync SharedPreferences when AuthService data changes
        if (authService.currentPatient != null && !_isLoading) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            if (mounted) {
              final persistenceService = DataPersistenceService();
              await persistenceService.savePatientProfile(
                authService.currentPatient!,
              );
              await _loadUserData(); // Refresh UI with updated data
            }
          });
        }

        if (_isLoading) {
          return Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.grey.withValues(alpha: 0.3),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 60,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 120,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Icon(
                  Icons.notifications_none_outlined,
                  size: 32,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.primaryColor.withValues(alpha: 0.08),
                AppTheme.primaryColor.withValues(alpha: 0.03),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                blurRadius: 15,
                offset: const Offset(0, 4),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PatientProfilePage(),
                    ),
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 30,
                    backgroundColor: AppTheme.primaryColor.withValues(
                      alpha: 0.1,
                    ),
                    backgroundImage:
                        _profileImage != null
                            ? FileImage(_profileImage!)
                            : const AssetImage(
                                  'assets/icons/default_avatar.png',
                                )
                                as ImageProvider,
                    child:
                        _profileImage == null
                            ? Text(
                              _userData['firstName']!.isNotEmpty
                                  ? _userData['firstName']!
                                      .substring(0, 1)
                                      .toUpperCase()
                                  : "U",
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                            )
                            : null,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    Text(
                      'Welcome To BeFine',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _userData['firstName']!,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Day 3', // Keep treatment day counter for future development
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      _notificationsEnabled
                          ? AppTheme.primaryColor.withValues(alpha: 0.1)
                          : Colors.grey.withValues(alpha: 0.1),
                ),
                child: IconButton(
                  icon: Icon(
                    _notificationsEnabled
                        ? Icons.notifications_active
                        : Icons.notifications_off,
                    size: 28,
                    color:
                        _notificationsEnabled
                            ? AppTheme.primaryColor
                            : Colors.grey,
                  ),
                  onPressed: _toggleNotifications,
                  tooltip:
                      _notificationsEnabled
                          ? 'Disable notifications'
                          : 'Enable notifications',
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
