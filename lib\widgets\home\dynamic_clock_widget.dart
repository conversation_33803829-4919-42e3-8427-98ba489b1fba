import 'package:flutter/material.dart';
import 'dart:math' as math;

class DynamicClockWidget extends StatefulWidget {
  final int minutesRemaining;

  const DynamicClockWidget({
    Key? key,
    required this.minutesRemaining,
  }) : super(key: key);

  @override
  State<DynamicClockWidget> createState() => _DynamicClockWidgetState();
}

class _DynamicClockWidgetState extends State<DynamicClockWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late DateTime _targetTime;

  @override
  void initState() {
    super.initState();
    
    // Calculate target time (current time + minutes remaining)
    _targetTime = DateTime.now().add(Duration(minutes: widget.minutesRemaining));
    
    // Setup animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        // Get current time
        final now = DateTime.now();
        
        // Calculate remaining time
        final remaining = _targetTime.difference(now);
        final minutesRemaining = remaining.inMinutes;
        final secondsRemaining = remaining.inSeconds % 60;
        
        // Calculate hand angles
        final minuteAngle = (minutesRemaining / 60) * 2 * math.pi - math.pi / 2;
        final secondAngle = (secondsRemaining / 60) * 2 * math.pi - math.pi / 2;
        
        return Stack(
          children: [
            // Clock face
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                shape: BoxShape.circle,
              ),
            ),
            
            // Clock center
            Center(
              child: Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            
            // Clock markings
            ...List.generate(12, (index) {
              final angle = (index / 12) * 2 * math.pi - math.pi / 2;
              final isHour = index % 3 == 0;
              
              return Positioned.fill(
                child: Align(
                  alignment: Alignment.center,
                  child: Transform.rotate(
                    angle: angle,
                    child: Transform.translate(
                      offset: const Offset(0, -30),
                      child: Container(
                        width: isHour ? 4 : 2,
                        height: isHour ? 8 : 4,
                        decoration: BoxDecoration(
                          color: isHour ? Colors.blue : Colors.blue.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
            
            // Minute hand
            Positioned.fill(
              child: CustomPaint(
                painter: ClockHandPainter(
                  angle: minuteAngle,
                  handLength: 22,
                  handWidth: 4,
                  color: Colors.blue,
                ),
              ),
            ),
            
            // Second hand
            Positioned.fill(
              child: CustomPaint(
                painter: ClockHandPainter(
                  angle: secondAngle,
                  handLength: 25,
                  handWidth: 2,
                  color: Colors.red,
                ),
              ),
            ),
            
            // Center dot
            Center(
              child: Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class ClockHandPainter extends CustomPainter {
  final double angle;
  final double handLength;
  final double handWidth;
  final Color color;

  ClockHandPainter({
    required this.angle,
    required this.handLength,
    required this.handWidth,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Calculate hand endpoint
    final x = center.dx + handLength * math.cos(angle);
    final y = center.dy + handLength * math.sin(angle);
    final endPoint = Offset(x, y);
    
    // Draw hand
    final paint = Paint()
      ..color = color
      ..strokeWidth = handWidth
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(center, endPoint, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

